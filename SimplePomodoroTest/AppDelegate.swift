import Cocoa



func writeDebugLog(_ message: String) {
    let logMessage = "\(Date()): \(message)\n"
    let logPath = "/tmp/uProd_debug.log"
    if let data = logMessage.data(using: .utf8) {
        if FileManager.default.fileExists(atPath: logPath) {
            if let fileHandle = FileHandle(forWritingAtPath: logPath) {
                fileHandle.seekToEndOfFile()
                fileHandle.write(data)
                fileHandle.closeFile()
            }
        } else {
            try? data.write(to: URL(fileURLWithPath: logPath))
        }
    }
}

// MARK: - Константы приложения

/// Версия приложения для отображения в меню
/// Если не удается получить версию из Bundle, используется эта константа
private let APP_VERSION_FALLBACK = "0.2.4"

// MARK: - Unified Reminder System
enum ReminderType {
    case formalIntervalCompleted    // Формальный интервал завершен
    case informalWorkDetected       // Обнаружена неформальная работа
    case continuousWorkReminder(minutes: Int, level: Int) // Напоминание о непрерывной работе
}

// MARK: - Completion Window Types (legacy)
enum CompletionWindowType {
    case formalInterval
    case informalSuggestion
    case continuousWorkReminder(minutes: Int, level: Int)
}

class AppDelegate: NSObject, NSApplicationDelegate, SimpleUnifiedSystemDelegate, CriticalZoneWindowDelegate {

    var statusItem: NSStatusItem!
    var pomodoroTimer: PomodoroTimer!
    var notificationWindow: IntervalNotificationWindow?
    var modernCompletionWindow: ModernCompletionWindow? // Новое современное окно
    var criticalZoneWindow: CriticalZoneWindow? // Критическое полноэкранное окно
    var countdownWindow: CountdownWindow? // Окно обратного отсчета перед критическим окном
    var settingsWindow: SettingsWindow?
    var statisticsManager: StatisticsManager!
    var statisticsWindow: StatisticsWindow?
    var workPatternAnalyzer: WorkPatternAnalyzer!
    var demoDataManager: DemoDataManager!
    var projectManager: ProjectManager!
    var projectsWindow: ProjectsWindow?
    var quickProjectSelectionWindow: QuickProjectSelectionWindow?
    var soundManager: SoundManager!
    var computerTimeTracker: ComputerTimeTracker!

    // Детектор неформальных сессий
    var informalSessionDetector: InformalSessionDetector!

    // Менеджер состояний активности (унифицированная система)
    var activityStateManager: ActivityStateManager!

    // Система напоминаний после "Just a few minutes"
    private var continuousWorkTimer: Timer?
    private var continuousWorkStartTime: Date?
    private var lastContinuousWorkLevel: Int = -1
    private var continuousWorkPausedTime: Date? // Когда остановили счетчик
    private var totalPausedDuration: TimeInterval = 0 // Общее время пауз

    // Счетчик для логирования
    private var updateCounter = 0

    // Окна отдыха
    var breakStartWindow: BreakStartWindow?
    var breakEndWindow: BreakEndWindow?
    var breakTypeWindow: BreakTypeSelectionWindow?

    // Таймер для отложенного напоминания "Later" (10 минут)
    private var postponeTimer: Timer?

    // Отложенный отдых - зеленый счетчик
    private var isBreakPostponed = false
    private var postponedBreakStartTime: Date?
    private var originalBreakDuration: TimeInterval = 0  // Сколько длился оригинальный отдых
    private var postponeUpdateTimer: Timer?

    var motivationManager: MotivationManager!

    // Флаг для режима тестирования (не проверяем активность)
    var isTestMode: Bool = false

    // Текущий выбранный проект для запуска интервала
    var currentProjectId: UUID?

    // Флаги для отслеживания поведения во время отдыха
    private var userPromisedToRest = false // Пользователь нажал "Иду отдыхать"
    private var userRestingAtComputer = false // Пользователь нажал "Я отдыхаю за компом"

    // Отслеживание сна системы для сброса состояний
    private var systemSleepStartTime: Date?

    // Последний использованный проект (для показа в окне завершения отдыха)
    var lastUsedProjectId: UUID?

    // Качество последнего отдыха
    var lastBreakQuality: Int = 100

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        print("🚀 Запуск приложения")
        NSLog("🚀 uProd: App starting...")

        // Скрываем из Dock
        NSApp.setActivationPolicy(.accessory)
        print("🔧 Режим: accessory")
        NSLog("🚀 uProd: Set activation policy to accessory")

        // Инициализируем автозапуск
        // LaunchAtLoginManager.shared.initializeOnFirstLaunch()
        print("⚙️ Автозапуск настроен")
        NSLog("🚀 uProd: Launch at login initialized")

        // Инициализируем статистику
        setupStatistics()
        print("📊 Статистика готова")
        NSLog("🚀 uProd: Statistics setup complete")

        // Инициализируем таймер
        setupPomodoroTimer()
        print("⏰ Таймер готов")
        NSLog("🚀 uProd: Timer setup complete")

        // Инициализируем новую простую систему
        setupSimpleUnifiedSystem()
        print("🔧 Простая система готова")
        NSLog("🚀 uProd: Simple unified system setup complete")

        // Создаём menu bar item
        setupStatusItem()
        print("📍 Меню готово")
        NSLog("🚀 uProd: Status item setup complete")

        print("✅ Инициализация завершена")
        NSLog("🚀 uProd: App fully initialized")

        // Логируем начальное состояние памяти
        // Logger.shared.logMemoryUsage()

        // Тестируем звуковую систему
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            print("Запуск тестирования звуковой системы")
            // SoundTest.runTest()
        }

        // ОТКЛЮЧЕНО: Автоматический запуск теста неформального интервала
        // DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
        //     logInfo("AutoTest", "🤖 Автоматический запуск теста неформального интервала")
        //     self.simulateInformalBreakFlow()
        // }

        // Тесты кнопок отключены - используем новый кастомный интерфейс
        // DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
        //     ButtonTestManager.shared.runButtonTests()
        // }

        // Настраиваем унифицированную обработку сна/пробуждения системы
        setupUnifiedSleepWakeDetector()
        // setupSleepWakeNotifications() // Старая система отключена
        print("💤 Обработка сна/пробуждения настроена (legacy)")
        NSLog("🚀 uProd: Sleep/wake notifications setup complete")

    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        // Не завершаем приложение при закрытии последнего окна
        return false
    }

    func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }

    private func setupStatistics() {
        print("📊 Настройка статистики")

        statisticsManager = StatisticsManager()
        projectManager = ProjectManager()
        workPatternAnalyzer = WorkPatternAnalyzer(statisticsManager: statisticsManager)
        motivationManager = MotivationManager(statisticsManager: statisticsManager, analyzer: workPatternAnalyzer)
        demoDataManager = DemoDataManager(statisticsManager: statisticsManager)
        soundManager = SoundManager()

        // Инициализируем и запускаем трекер времени за компьютером
        computerTimeTracker = ComputerTimeTracker(statisticsManager: statisticsManager)
        computerTimeTracker.startTracking()

        // Проверяем и исправляем настройки звуков
        // SoundSettings.shared.validateAndFixSettings()

        print("✅ Статистика готова")
    }

    private func setupPomodoroTimer() {
        pomodoroTimer = PomodoroTimer()

        // Связываем SoundManager с таймерами
        pomodoroTimer.soundManager = soundManager
        pomodoroTimer.breakTimer.soundManager = soundManager

        // Настраиваем колбэки
        pomodoroTimer.onStateChanged = { [weak self] state in
            DispatchQueue.main.async {
                // ТОЛЬКО для НЕ-переработки состояний - для переработки используем SimpleUnifiedSystem
                if state != .overtime {
                    self?.updateStatusItem()
                }
                self?.updateMenu()

                // Сбрасываем историю активности при начале интервала
                if state == .working {
                    self?.informalSessionDetector?.resetActivityHistory()
                }
            }
        }

        pomodoroTimer.onTimeUpdate = { [weak self] (timeRemaining: TimeInterval, overtimeElapsed: TimeInterval) in
            DispatchQueue.main.async {
                // ТОЛЬКО для рабочего времени и отдыха - НЕ для переработки (используем SimpleUnifiedSystem)
                if self?.pomodoroTimer.state != .overtime {
                    self?.updateStatusItem()
                }
            }
        }

        pomodoroTimer.onIntervalCompleted = { [weak self] in
            DispatchQueue.main.async {
                self?.showCompletionWindow()
            }
        }

        // ОТКЛЮЧЕНО: Старая логика напоминаний заменена на onOvertimeColorChanged
        // pomodoroTimer.onReminderTriggered = { [weak self] reminderCount in
        //     DispatchQueue.main.async {
        //         self?.showReminderWindow(reminderCount: reminderCount)
        //     }
        // }

        // УНИФИЦИРОВАННАЯ СИСТЕМА: Используем SimpleUnifiedSystem для формальных интервалов
        pomodoroTimer.onOvertimeColorChanged = { [weak self] (colorLevel: Int) in
            DispatchQueue.main.async {
                self?.handleFormalOvertimeWithUnifiedSystem(colorLevel: colorLevel)
            }
        }

        // Настраиваем проверку активности для формальных интервалов (единая система)
        pomodoroTimer.onCheckUserActivity = {
            // Используем единую систему проверки активности для всех типов интервалов
            return UnifiedActivityChecker.shared.isUserCurrentlyActive()
        }

        pomodoroTimer.onFullIntervalCompleted = { [weak self] duration in
            DispatchQueue.main.async {
                // УНИФИЦИРОВАННАЯ СТАТИСТИКА: Записываем с типом интервала
                self?.statisticsManager.recordCompletedInterval(duration: duration, projectId: self?.currentProjectId, intervalType: "formal")
                self?.motivationManager.checkAndSendMotivationalNotification()
                // Сохраняем последний использованный проект для показа в окне завершения отдыха
                self?.lastUsedProjectId = self?.currentProjectId
                // Сбрасываем текущий проект после завершения интервала
                self?.currentProjectId = nil
            }
        }

        pomodoroTimer.onBreakStarted = { [weak self] in
            DispatchQueue.main.async {
                // НЕ показываем окно сразу - только запускаем отдых
                print("🍃 AppDelegate: Отдых начат, мониторинг активности запущен")
            }
        }

        pomodoroTimer.onBreakCompleted = { [weak self] breakStats in
            DispatchQueue.main.async {
                // Сбрасываем флаги отдыха
                self?.userPromisedToRest = false
                self?.userRestingAtComputer = false

                // СРАЗУ запускаем зеленый счетчик для бесшовного перехода
                self?.startPostponedBreakCounter(with: breakStats)

                // Затем показываем окно
                self?.showBreakEndWindow()
            }
        }

        pomodoroTimer.onBreakActivityDetected = { [weak self] in
            DispatchQueue.main.async {
                self?.showBreakActivityWarning()
            }
        }

        // Обработка качества отдыха из BreakTimer
        pomodoroTimer.breakTimer.onBreakQualityCalculated = { [weak self] (qualityPercentage: Int) in
            DispatchQueue.main.async {
                self?.lastBreakQuality = qualityPercentage
                print("📊 AppDelegate: Получено качество отдыха: \(qualityPercentage)%")
            }
        }

        // ВРЕМЕННО ЗАКОММЕНТИРОВАНО: Обработка полноценных отдыхов (записываются в статистику)
        // pomodoroTimer.onFullBreakCompleted = { [weak self] stats in
        //     DispatchQueue.main.async {
        //         self?.statisticsManager.recordCompletedBreak(
        //             duration: stats.duration,
        //             wasComputerActive: stats.wasComputerActive,
        //             computerActiveTime: stats.computerActiveTime,
        //             qualityPercentage: self?.lastBreakQuality ?? 100
        //         )
        //     }
        // }

        // ВРЕМЕННО ЗАКОММЕНТИРОВАНО: Обработка тестовых отдыхов (НЕ записываются в статистику)
        // pomodoroTimer.onBreakStatisticsReady = { [weak self] stats in
        //     // Этот колбэк теперь используется только для тестовых отдыхов
        //     // Ничего не записываем в статистику
        //     print("🧪 AppDelegate: Тестовый отдых завершен, не записываем в статистику")
        // }

        // ОТКЛЮЧЕНО: Настраиваем детектор неформальных сессий (заменен на ActivityStateManager)
        // setupInformalSessionDetector()

        // Настраиваем менеджер состояний активности
        logInfo("App", "🎯 ОТЛАДКА: Вызываем setupActivityStateManager()")
        setupActivityStateManager()

        // Настраиваем простую единую систему напоминаний
        SimpleUnifiedSystem.shared.delegate = self
    }

    /// Настраивает новую простую единую систему
    private func setupSimpleUnifiedSystem() {
        // Устанавливаем делегат для новой простой системы
        SimpleUnifiedSystem.shared.delegate = self
        print("🔧 SimpleUnifiedSystem делегат установлен")
    }

    /// Настраивает менеджер состояний активности
    private func setupActivityStateManager() {
        logInfo("App", "🎯 ОТЛАДКА: Начинаем создание ActivityStateManager")
        activityStateManager = ActivityStateManager()
        logInfo("App", "🎯 ОТЛАДКА: ActivityStateManager создан")

        // Настраиваем колбэки
        activityStateManager.onStateChanged = { [weak self] previousState, newState in
            print("🎯 ActivityStateManager: Состояние изменено \(previousState) → \(newState)")
            // Здесь можно добавить дополнительную логику при изменении состояния
        }

        activityStateManager.onStopCounters = { [weak self] in
            print("🎯 ActivityStateManager: Остановка счетчиков из-за неактивности")
            // Приостанавливаем таймер PomodoroTimer
            self?.pomodoroTimer?.pauseAllTimers()
            logInfo("App", "🎯 Таймер приостановлен из-за неактивности")
        }

        activityStateManager.onResumeCounters = { [weak self] in
            print("🎯 ActivityStateManager: Возобновление счетчиков после возвращения")
            // Возобновляем таймер PomodoroTimer
            self?.pomodoroTimer?.resumeAllTimers()
            logInfo("App", "🎯 Таймер возобновлен после возвращения активности")
        }

        activityStateManager.onUserReturned = { [weak self] message, duration in
            let minutes = Int(duration / 60)
            logInfo("App", "🎯 ActivityStateManager: Пользователь вернулся после \(minutes) мин, сообщение: \(message)")
            self?.showReturnWindow(message: message, duration: duration)
        }

        activityStateManager.onLongInactivity = { [weak self] duration in
            let minutes = Int(duration / 60)
            logInfo("App", "🔄 ActivityStateManager: Длительная неактивность (\(minutes) мин) - закрываем окна")
            self?.closeNotificationWindows()
        }

        // Запускаем менеджер
        logInfo("App", "🎯 ОТЛАДКА: Готовы вызвать start()")
        logInfo("App", "🎯 ОТЛАДКА: activityStateManager = \(activityStateManager != nil ? "НЕ NIL" : "NIL")")
        activityStateManager?.start()
        logInfo("App", "🎯 ОТЛАДКА: start() вызван")

        // КРИТИЧЕСКИ ВАЖНО: Передаем MinuteActivityTracker в PomodoroTimer для унификации
        if let activityStateManager = activityStateManager {
            pomodoroTimer.setMinuteActivityTracker(activityStateManager.minuteTracker)
            logInfo("App", "🎯 MinuteActivityTracker передан в PomodoroTimer для унификации")
        }

        print("🎯 ActivityStateManager настроен и запущен")
    }

    private func setupInformalSessionDetector() {
        // Инициализируем детектор
        informalSessionDetector = InformalSessionDetector(
            pomodoroTimer: pomodoroTimer,
            breakTimer: pomodoroTimer.breakTimer
        )

        // Подключаем к трекеру времени за компьютером
        computerTimeTracker.onActivityRecorded = { [weak self] isActive in
            self?.informalSessionDetector?.recordMinuteActivity(isActive: isActive)
        }

        // Настраиваем callback для показа предложения отдыха
        informalSessionDetector.onRestSuggestionNeeded = { [weak self] in
            DispatchQueue.main.async {
                self?.showRestSuggestion()
            }
        }

        // Устанавливаем начальное состояние из настроек (включен по умолчанию)
        let isEnabled = UserDefaults.standard.object(forKey: "InformalSessionDetectorEnabled") as? Bool ?? true
        UserDefaults.standard.set(isEnabled, forKey: "InformalSessionDetectorEnabled") // Сохраняем значение по умолчанию
        informalSessionDetector.setEnabled(isEnabled)

        print("✅ Детектор неформальных сессий настроен (включен: \(isEnabled))")
    }

    private func setupStatusItem() {
        NSLog("🚀 uProd: Creating status item...")
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)

        if let button = statusItem.button {
            button.title = pomodoroTimer.getStatusText()
            button.toolTip = "uProd - Productivity Timer"
            button.action = #selector(statusItemClicked)
            button.target = self
            NSLog("🚀 uProd: Status item button configured with title: \(button.title)")
        } else {
            NSLog("❌ uProd: Failed to get status item button")
        }

        // Создаем меню
        writeDebugLog("🔧 AppDelegate: setupStatusItem вызывает updateMenu")
        updateMenu()
        writeDebugLog("🔧 AppDelegate: setupStatusItem updateMenu завершен")

        // Подписываемся на уведомления об изменении избранных
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(favoritesChanged),
            name: NSNotification.Name("FavoritesChanged"),
            object: nil
        )

        // Подписываемся на уведомления об изменении порядка проектов
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(projectOrderChanged),
            name: NSNotification.Name("ProjectOrderChanged"),
            object: nil
        )

        NSLog("🚀 uProd: Status item setup complete")
    }

    @objc private func statusItemClicked() {
        // При клике показываем меню
        // Меню автоматически показывается при клике, если оно установлено
    }

    private func updateStatusItem() {
        // Логируем каждое 10-е обновление для избежания спама
        updateCounter += 1

        if updateCounter % 10 == 0 {
            print("🔄 Status update #\(updateCounter)")
        }

        // Получаем название проекта если есть
        var projectName: String? = nil
        if let projectId = currentProjectId,
           let project = projectManager.getProject(by: projectId) {
            projectName = project.effectiveEmoji
            print("🎯 AppDelegate: updateStatusItem - проект найден: \(project.name) (\(project.effectiveEmoji))")
        } else {
            print("🎯 AppDelegate: updateStatusItem - currentProjectId=\(currentProjectId?.uuidString ?? "nil")")
        }

        // Если отдых отложен, показываем зеленый счетчик
        let statusText: String
        if isBreakPostponed {
            // Во время отложенного отдыха НЕ показываем иконку проекта - только листочек и время
            statusText = getPostponedBreakStatusText(projectName: nil)
        } else {
            // Передаем projectName только во время работы и переработки, не во время отдыха
            let projectForStatus = (pomodoroTimer.state == .working || pomodoroTimer.state == .overtime) ? projectName : nil
            statusText = pomodoroTimer.getStatusText(projectName: projectForStatus)
        }

        // Проверяем, нужен ли красный фон (с 10 минут переработки)
        let needsRedBackground = pomodoroTimer.state == .overtime && pomodoroTimer.overtimeElapsed >= 600 // 10 минут = 600 секунд

        if needsRedBackground {
            // Создаем изображение с красным фоном и белым текстом
            let image = createRedBackgroundImage(with: statusText)
            statusItem.button?.image = image
            statusItem.button?.title = ""
            statusItem.button?.attributedTitle = NSAttributedString(string: "")
        } else {
            // Обычный цветной текст без фона
            statusItem.button?.image = nil

            let textColor: NSColor
            if isBreakPostponed {
                textColor = NSColor(red: 0.5, green: 0.8, blue: 0.5, alpha: 1.0) // Тот же зеленый как у обычного отдыха
            } else if pomodoroTimer.state == .overtime {
                textColor = getOvertimeTextColor()
            } else if pomodoroTimer.state == .onBreak {
                textColor = NSColor(red: 0.5, green: 0.8, blue: 0.5, alpha: 1.0) // Более насыщенный зеленый для отдыха
            } else if continuousWorkStartTime != nil {
                // Цвет для счетчика непрерывной работы (как в переработке)
                textColor = getContinuousWorkTextColor()
            } else {
                textColor = NSColor.controlTextColor
            }

            // Полностью очищаем button
            statusItem.button?.title = ""
            statusItem.button?.attributedTitle = NSAttributedString(string: "")

            // Создаем attributed string с цветом для каждого символа
            let attributedString = NSMutableAttributedString()
            let font = NSFont.monospacedSystemFont(ofSize: 13, weight: .medium)

            // Добавляем каждый символ отдельно с принудительным цветом
            for char in statusText {
                let charString = String(char)
                let charAttributed = NSAttributedString(string: charString, attributes: [
                    .foregroundColor: textColor,
                    .font: font
                ])
                attributedString.append(charAttributed)
            }

            // Устанавливаем с принудительным обновлением
            statusItem.button?.attributedTitle = attributedString

            // Принудительно перерисовываем весь status item
            if let button = statusItem.button {
                button.setNeedsDisplay(button.bounds)
            }
        }
    }

    private func getOvertimeTextColor() -> NSColor {
        let minutes = Int(pomodoroTimer.overtimeElapsed) / 60
        return OvertimeConfig.getColor(for: minutes)
    }

    private func getContinuousWorkTextColor() -> NSColor {
        guard let startTime = continuousWorkStartTime else { return NSColor.controlTextColor }
        let workDuration = Date().timeIntervalSince(startTime)
        let workMinutes = Int(workDuration / 60)
        return OvertimeConfig.getColor(for: workMinutes)
    }

    private func createRedBackgroundImage(with text: String) -> NSImage {
        let font = NSFont.monospacedSystemFont(ofSize: 13, weight: .medium)
        let textAttributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: NSColor.white
        ]

        // Измеряем размер текста
        let textSize = text.size(withAttributes: textAttributes)

        // Делаем фон на всю высоту status bar (обычно 22px) с отступами слева-справа
        let statusBarHeight: CGFloat = 22
        let horizontalPadding: CGFloat = 6
        let imageSize = NSSize(width: textSize.width + horizontalPadding * 2, height: statusBarHeight)

        // Создаем изображение
        let image = NSImage(size: imageSize)
        image.lockFocus()

        // Рисуем красный фон на всю высоту
        let backgroundRect = NSRect(origin: .zero, size: imageSize)
        NSColor.systemRed.setFill()
        backgroundRect.fill()

        // Рисуем белый текст по центру
        let textRect = NSRect(
            x: horizontalPadding,
            y: (imageSize.height - textSize.height) / 2,
            width: textSize.width,
            height: textSize.height
        )
        text.draw(in: textRect, withAttributes: textAttributes)

        image.unlockFocus()

        // НЕ устанавливаем template - чтобы сохранить красный цвет
        image.isTemplate = false

        return image
    }

    private func updateMenu() {
        writeDebugLog("🔧 AppDelegate: updateMenu вызван")
        let menu = NSMenu()

        if pomodoroTimer.isActive() {
            // Показываем информацию о текущем состоянии
            let statusItem = NSMenuItem(title: getStatusMenuText(), action: nil, keyEquivalent: "")
            statusItem.isEnabled = false
            menu.addItem(statusItem)

            menu.addItem(NSMenuItem.separator())

            // Кнопка завершения интервала
            let stopItem = NSMenuItem(title: "Завершить интервал", action: #selector(stopInterval), keyEquivalent: "")
            stopItem.target = self
            menu.addItem(stopItem)
        } else {
            // Избранные проекты для быстрого запуска
            let favoriteProjects = projectManager.getFavoriteProjects()
            let minutes = Int(PomodoroTimer.workDuration / 60)

            if favoriteProjects.isEmpty {
                // Если нет избранных проектов, показываем обычную кнопку
                let startItem = NSMenuItem(title: "Начать интервал (\(minutes) мин)", action: #selector(startInterval), keyEquivalent: "")
                startItem.target = self
                menu.addItem(startItem)
            } else {
                // Показываем кнопки для каждого избранного проекта
                for (index, project) in favoriteProjects.enumerated() {
                    let colorIndicator = createColorIndicator(for: project)
                    let title = "\(colorIndicator) Начать: \(project.name) (\(minutes) мин)"
                    let selector = #selector(startIntervalWithProject(_:))
                    let keyEquivalent = index < 3 ? "\(index + 1)" : ""

                    let projectItem = NSMenuItem(title: title, action: selector, keyEquivalent: keyEquivalent)
                    projectItem.target = self
                    projectItem.representedObject = project.id
                    menu.addItem(projectItem)
                }
            }

            menu.addItem(NSMenuItem.separator())
        }

        menu.addItem(NSMenuItem.separator())

        // Кнопка управления проектами
        let projectsItem = NSMenuItem(title: "📁 Проекты...", action: #selector(showProjectManagement), keyEquivalent: "p")
        projectsItem.target = self
        menu.addItem(projectsItem)
        writeDebugLog("🔧 AppDelegate: добавлен пункт меню 'Проекты...', target: \(String(describing: projectsItem.target)), action: \(String(describing: projectsItem.action))")

        // Кнопка настроек
        let settingsItem = NSMenuItem(title: "⚙️ Настройки...", action: #selector(showSettings), keyEquivalent: ",")
        settingsItem.target = self
        menu.addItem(settingsItem)

        // Кнопка статистики
        let statisticsItem = NSMenuItem(title: "📊 Статистика...", action: #selector(showStatistics), keyEquivalent: "s")
        statisticsItem.target = self
        menu.addItem(statisticsItem)

        menu.addItem(NSMenuItem.separator())

        // Кнопка тестового запуска (остается в основном меню)
        let testItem = NSMenuItem(title: "🧪 Тестовый запуск (3 сек)", action: #selector(startTestInterval), keyEquivalent: "t")
        testItem.target = self
        menu.addItem(testItem)

        // Создаем подменю "Другие тесты"
        let otherTestsItem = NSMenuItem(title: "🧪 Другие тесты", action: nil, keyEquivalent: "")
        let otherTestsMenu = createOtherTestsMenu()
        otherTestsItem.submenu = otherTestsMenu
        menu.addItem(otherTestsItem)

        menu.addItem(NSMenuItem.separator())

        // Отладочная информация о неформальных сессиях
        let debugInformalItem = NSMenuItem(title: "🔍 Статус неформальных сессий", action: #selector(showInformalSessionDebug), keyEquivalent: "")
        debugInformalItem.target = self
        menu.addItem(debugInformalItem)

        menu.addItem(NSMenuItem.separator())

        // Пункт меню для работы с логами
        let openLogsItem = NSMenuItem(title: "📋 Открыть папку логов", action: #selector(openLogsFolder), keyEquivalent: "l")
        openLogsItem.target = self
        menu.addItem(openLogsItem)

        menu.addItem(NSMenuItem.separator())

        // Версия приложения
        let versionItem = NSMenuItem(title: "Версия \(getAppVersion())", action: nil, keyEquivalent: "")
        versionItem.isEnabled = false
        menu.addItem(versionItem)

        menu.addItem(NSMenuItem.separator())

        // Кнопка выхода
        let quitItem = NSMenuItem(title: "🚪 Выход", action: #selector(quitApp), keyEquivalent: "q")
        quitItem.target = self
        menu.addItem(quitItem)

        statusItem.menu = menu
    }

    private func getStatusMenuText() -> String {
        switch pomodoroTimer.state {
        case .idle:
            // Показываем счетчик непрерывной работы если активен
            if let startTime = continuousWorkStartTime {
                let workDuration = Date().timeIntervalSince(startTime)
                let workMinutes = Int(workDuration / 60)
                let workSeconds = Int(workDuration) % 60

                // Определяем цвет по уровню эскалации (как в переработке)
                let level = OvertimeConfig.getLevelNumber(for: workMinutes)
                let emoji = level > 0 ? "⚠️" : "⏱️"

                return "\(emoji) +\(String(format: "%d:%02d", workMinutes, workSeconds))"
            }
            return "Готов к работе"
        case .working:
            let timeText = "Работаем: \(pomodoroTimer.formatTime(pomodoroTimer.timeRemaining))"
            if let projectId = currentProjectId,
               let project = projectManager.getProject(by: projectId) {
                return "\(project.effectiveEmoji) \(project.name) - \(timeText)"
            }
            return timeText
        case .overtime:
            let timeText = "Переработка: +\(pomodoroTimer.formatTime(pomodoroTimer.overtimeElapsed))"
            if let projectId = currentProjectId,
               let project = projectManager.getProject(by: projectId) {
                return "\(project.effectiveEmoji) \(project.name) - \(timeText)"
            }
            return timeText
        case .onBreak:
            return "🍃 Отдых: \(pomodoroTimer.formatTime(pomodoroTimer.breakTimeRemaining))"
        }
    }

    private func getAppVersion() -> String {
        // Сначала пытаемся прочитать версию из todo.md
        if let version = readVersionFromTodoMd() {
            return version
        }

        // Если не получилось, берем из Bundle
        if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            return version
        }

        // Последний fallback
        return APP_VERSION_FALLBACK
    }

    private func readVersionFromTodoMd() -> String? {
        // Ищем файл todo.md в корне проекта
        let currentDirectory = FileManager.default.currentDirectoryPath
        let todoPath = "\(currentDirectory)/todo.md"

        // Если файл не найден в текущей директории, пробуем относительный путь
        let possiblePaths = [
            todoPath,
            "\(currentDirectory)/../todo.md",
            "\(currentDirectory)/../../todo.md"
        ]

        for path in possiblePaths {
            if let content = try? String(contentsOfFile: path, encoding: .utf8) {
                // Ищем строку с APP_VERSION =
                let lines = content.components(separatedBy: .newlines)
                for line in lines {
                    if line.contains("APP_VERSION") && line.contains("=") {
                        // Извлекаем версию из строки типа "**APP_VERSION = 0.2.4**"
                        let components = line.components(separatedBy: "=")
                        if components.count >= 2 {
                            let versionPart = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                            // Убираем возможные символы markdown (**) и пробелы
                            let cleanVersion = versionPart.replacingOccurrences(of: "*", with: "").trimmingCharacters(in: .whitespacesAndNewlines)
                            if !cleanVersion.isEmpty {
                                print("📋 AppDelegate: Версия из todo.md: \(cleanVersion)")
                                return cleanVersion
                            }
                        }
                    }
                }
            }
        }

        print("📋 AppDelegate: Не удалось прочитать версию из todo.md")
        return nil
    }

    @objc private func startInterval() {
        currentProjectId = nil
        stopContinuousWorkTracking() // Останавливаем отслеживание непрерывной работы
        pomodoroTimer.startInterval()
    }

    @objc private func startIntervalWithProject(_ sender: NSMenuItem) {
        if let projectId = sender.representedObject as? UUID {
            currentProjectId = projectId
            projectManager.markProjectAsUsed(projectId)
            stopContinuousWorkTracking() // Останавливаем отслеживание непрерывной работы
            pomodoroTimer.startInterval()
        }
    }



    // MARK: - Отладочные методы

    @objc private func showInformalSessionDebug() {
        // Получаем данные из ActivityStateManager (новая система)
        guard let activityManager = activityStateManager else {
            let alert = NSAlert()
            alert.messageText = "❌ Ошибка"
            alert.informativeText = "ActivityStateManager не инициализирован"
            alert.addButton(withTitle: "OK")
            alert.runModal()
            return
        }

        // Получаем данные активности за последние 52 минуты
        let minuteTracker = activityManager.minuteTracker
        let recentActivity = minuteTracker.getRecentActivity(minutes: 52)
        let activeCount = recentActivity.filter { $0 }.count
        let totalMinutes = recentActivity.count
        let minActiveMinutes = 42 // Требуется для срабатывания (80% от 52)

        // Проверяем все условия как в старой системе
        let hasEnoughData = totalMinutes >= 52
        let hasEnoughActivity = activeCount >= minActiveMinutes
        let timerIdle = isPomodoroTimerIdle()
        let wouldTrigger = hasEnoughData && hasEnoughActivity && timerIdle

        // Формируем информацию с проверкой статуса систем
        var info = "🔍 СТАТУС СИСТЕМ ОТСЛЕЖИВАНИЯ:\n"
        info += "================================\n"

        // Проверяем статус старой системы
        if informalSessionDetector != nil {
            info += "⚠️ СТАРАЯ СИСТЕМА: InformalSessionDetector АКТИВНА\n"
        } else {
            info += "✅ СТАРАЯ СИСТЕМА: InformalSessionDetector ОТКЛЮЧЕНА\n"
        }

        // Проверяем статус новой системы
        info += "✅ НОВАЯ СИСТЕМА: ActivityStateManager АКТИВНА\n"
        info += "- Включен: \(activityManager.isActive)\n"
        let stateInfo = activityManager.getCurrentStateInfo()
        info += "- Текущее состояние: \(stateInfo.state) (в состоянии \(Int(stateInfo.timeInState))с)\n"
        info += "\n"

        info += "📊 ДАННЫЕ АКТИВНОСТИ (новая система):\n"
        info += "- Всего минут в логе: \(totalMinutes)\n"
        info += "- Активных в последних 52: \(activeCount)/\(totalMinutes)\n"
        info += "- Требуется для срабатывания: \(minActiveMinutes)\n"
        info += "\n"
        info += "🔍 УСЛОВИЯ СРАБАТЫВАНИЯ:\n"
        info += "- Достаточно данных (≥52): \(hasEnoughData ? "ДА" : "НЕТ")\n"
        info += "- Достаточно активности (≥\(minActiveMinutes)): \(hasEnoughActivity ? "ДА" : "НЕТ")\n"
        info += "- Формальный таймер в покое: \(timerIdle ? "ДА" : "НЕТ")\n"
        info += "- Cooldown истек: ДА (новая система)\n"
        info += "\n"
        info += "🎯 РЕЗУЛЬТАТ: \(wouldTrigger ? "СРАБОТАЕТ" : "НЕ СРАБОТАЕТ")\n"
        info += "\n"

        // Добавляем кружочки активности как в старой версии
        info += "Лог активности (последние \(min(52, totalMinutes)) минут):\n"
        let displayCount = min(20, recentActivity.count)
        let displayActivity = Array(recentActivity.suffix(displayCount))

        for (index, isActive) in displayActivity.enumerated() {
            let symbol = isActive ? "🟢" : "⚫"
            info += "\(symbol)"
            if (index + 1) % 10 == 0 {
                info += "\n"
            }
        }

        info += "\n\nИтого: \(activeCount) активных из \(totalMinutes) минут"
        info += "\nТребуется: \(minActiveMinutes) для срабатывания"

        let alert = NSAlert()
        alert.messageText = "🔍 Статус неформальных сессий"
        alert.informativeText = info
        alert.addButton(withTitle: "OK")
        alert.addButton(withTitle: "📋 Копировать в буфер")

        let response = alert.runModal()
        if response == .alertSecondButtonReturn {
            let pasteboard = NSPasteboard.general
            pasteboard.clearContents()
            pasteboard.setString(info, forType: .string)

            let copyAlert = NSAlert()
            copyAlert.messageText = "✅ Скопировано"
            copyAlert.informativeText = "Отладочная информация скопирована в буфер обмена"
            copyAlert.addButton(withTitle: "OK")
            copyAlert.runModal()
        }
    }

    /// Проверяет, можно ли показать предложение отдыха (только состояние таймера)
    private func isPomodoroTimerIdle() -> Bool {
        // Проверяем состояние Pomodoro таймера
        guard pomodoroTimer.state == .idle else {
            return false
        }
        return true
    }

    private func getComputerTrackerDebugInfo() -> String {
        let isTracking = computerTimeTracker.isTracking
        let isActive = computerTimeTracker.isUserCurrentlyActive()
        let lastActivity = computerTimeTracker.lastActivityTime
        let timeSinceActivity = Date().timeIntervalSince(lastActivity)

        return """
        - Отслеживание активно: \(isTracking ? "ДА" : "НЕТ")
        - Пользователь активен: \(isActive ? "ДА" : "НЕТ")
        - Последняя активность: \(Int(timeSinceActivity)) сек назад
        - Время последней активности: \(DateFormatter.localizedString(from: lastActivity, dateStyle: .none, timeStyle: .medium))
        """
    }

    @objc private func startTestInterval() {
        // Запускаем тестовый интервал на 3 секунды
        currentProjectId = nil
        pomodoroTimer.startInterval(testDuration: 3)
    }

    @objc private func startTestBreak() {
        // Запускаем тестовый отдых на 3 секунды
        pomodoroTimer.startBreakWithType(isLong: false, testDuration: 3)
    }



    @objc private func stopInterval() {
        pomodoroTimer.stopInterval()
    }

    @objc private func showProjectManagement() {
        NSLog("🔧 AppDelegate: showProjectManagement вызван")
        if projectsWindow == nil {
            NSLog("🔧 AppDelegate: создаем новый ProjectsWindow")
            projectsWindow = ProjectsWindow(projectManager: projectManager)
        }
        NSLog("🔧 AppDelegate: вызываем showWindow")
        projectsWindow?.showWindow()
        NSLog("🔧 AppDelegate: showProjectManagement завершен")
    }

    // MARK: - Project Menu Creation

    private func createOtherProjectsMenu() -> NSMenu {
        let menu = NSMenu()
        let activeProjects = projectManager.getActiveProjects()
        let favoriteProjects = projectManager.getFavoriteProjects()
        let favoriteIds = Set(favoriteProjects.map { $0.id })

        // Группируем проекты по типам
        let projectsByType = Dictionary(grouping: activeProjects) { $0.type }
        let minutes = Int(PomodoroTimer.workDuration / 60)

        for projectType in ProjectType.allCases {
            if let projects = projectsByType[projectType], !projects.isEmpty {
                // Добавляем заголовок типа
                let typeHeader = NSMenuItem(title: projectType.displayName, action: nil, keyEquivalent: "")
                typeHeader.isEnabled = false
                menu.addItem(typeHeader)

                // Добавляем проекты этого типа
                for project in projects.sorted(by: { $0.name < $1.name }) {
                    // Пропускаем проекты, которые уже в избранном
                    if favoriteIds.contains(project.id) { continue }

                    let colorIndicator = createColorIndicator(for: project)
                    let title = "  \(colorIndicator) \(project.name) (\(minutes) мин)"
                    let projectItem = NSMenuItem(title: title, action: #selector(startIntervalWithProject(_:)), keyEquivalent: "")
                    projectItem.target = self
                    projectItem.representedObject = project.id
                    menu.addItem(projectItem)
                }

                menu.addItem(NSMenuItem.separator())
            }
        }

        // Удаляем последний разделитель если он есть
        if menu.items.last?.isSeparatorItem == true {
            menu.removeItem(menu.items.last!)
        }

        return menu
    }

    private func createOtherTestsMenu() -> NSMenu {
        let menu = NSMenu()

        // Кнопка тестового отдыха
        let testBreakItem = NSMenuItem(title: "🧪 Тестовый отдых (3 сек)", action: #selector(startTestBreak), keyEquivalent: "")
        testBreakItem.target = self
        menu.addItem(testBreakItem)

        // Кнопка тестирования активности
        let testActivityItem = NSMenuItem(title: "🔍 Тест активности", action: #selector(testActivitySystem), keyEquivalent: "")
        testActivityItem.target = self
        menu.addItem(testActivityItem)

        // Кнопка тестирования длительного сна
        let testSleepItem = NSMenuItem(title: "💤 Тест длительного сна", action: #selector(testLongSleep), keyEquivalent: "")
        testSleepItem.target = self
        menu.addItem(testSleepItem)

        // Кнопка тестирования неформального отдыха
        let testInformalBreakItem = NSMenuItem(title: "🧪 Тест неформального отдыха", action: #selector(testInformalBreak), keyEquivalent: "")
        testInformalBreakItem.target = self
        menu.addItem(testInformalBreakItem)

        menu.addItem(NSMenuItem.separator())

        // Автоматический тест всех сценариев
        let autoTestItem = NSMenuItem(title: "🤖 Автотест неформальных сессий", action: #selector(runAutomaticTests), keyEquivalent: "")
        autoTestItem.target = self
        menu.addItem(autoTestItem)

        menu.addItem(NSMenuItem.separator())

        // Живой тест окна неформальных интервалов
        let liveTestItem = NSMenuItem(title: "🚨 Живой тест окна", action: #selector(runLiveWindowTest), keyEquivalent: "")
        liveTestItem.target = self
        menu.addItem(liveTestItem)

        // Тест воспроизведения реальной проблемы
        let realProblemTestItem = NSMenuItem(title: "🔍 Воспроизвести реальную проблему", action: #selector(reproduceRealProblem), keyEquivalent: "")
        realProblemTestItem.target = self
        menu.addItem(realProblemTestItem)

        // Интегрированные тесты окна
        let integratedTestItem = NSMenuItem(title: "🧪 Интегрированные тесты окна", action: #selector(runIntegratedWindowTests), keyEquivalent: "")
        integratedTestItem.target = self
        menu.addItem(integratedTestItem)

        // Простой тест окна
        let simpleTestItem = NSMenuItem(title: "🪟 ПРОСТОЙ ТЕСТ - ПОЯВЛЯЕТСЯ ЛИ ОКНО?", action: #selector(runSimpleWindowTest), keyEquivalent: "")
        simpleTestItem.target = self
        menu.addItem(simpleTestItem)

        // Тест критического окна (исправление двойной кнопки)
        let criticalTestItem = NSMenuItem(title: "🚨 ТЕСТ КРИТИЧЕСКОГО ОКНА", action: #selector(testCriticalWindow), keyEquivalent: "")
        criticalTestItem.target = self
        menu.addItem(criticalTestItem)

        // Тест обратного отсчета перед критическим окном
        let countdownTestItem = NSMenuItem(title: "⏰ ТЕСТ ОБРАТНОГО ОТСЧЕТА", action: #selector(testCountdownWindow), keyEquivalent: "")
        countdownTestItem.target = self
        menu.addItem(countdownTestItem)

        menu.addItem(NSMenuItem.separator())

        // Тест сообщений при возвращении
        let returnMessageTestItem = NSMenuItem(title: "🎨 Демо сообщений при возвращении", action: #selector(testReturnMessages), keyEquivalent: "")
        returnMessageTestItem.target = self
        menu.addItem(returnMessageTestItem)

        // Тест новых окон возвращения - одна кнопка
        let oneButtonTestItem = NSMenuItem(title: "🪟 Демо окна (1 кнопка)", action: #selector(testReturnWindowOneButton), keyEquivalent: "")
        oneButtonTestItem.target = self
        menu.addItem(oneButtonTestItem)

        // Тест новых окон возвращения - две кнопки
        let twoButtonsTestItem = NSMenuItem(title: "🪟 Демо окна (2 кнопки)", action: #selector(testReturnWindowTwoButtons), keyEquivalent: "")
        twoButtonsTestItem.target = self
        menu.addItem(twoButtonsTestItem)

        menu.addItem(NSMenuItem.separator())

        // Тест реального SleepWakeDetector
        let sleepTestItem = NSMenuItem(title: "🌙 Тест реального сна (закройте крышку)", action: #selector(testRealSleepWakeDetector), keyEquivalent: "")
        sleepTestItem.target = self
        menu.addItem(sleepTestItem)

        return menu
    }

    @objc private func runAutomaticTests() {
        let alert = NSAlert()
        alert.messageText = "🤖 Автоматическое тестирование"
        alert.informativeText = """
        Будет запущено 8 тестовых сценариев:

        1. Идеальная работа (52 мин)
        2. Работа с перерывами (45+7 мин)
        3. Короткий сон (47 активных)
        4. Длительный сон (сброс)
        5. Мало активности (30 мин)
        6. Работа-уход-работа (реальный кейс)
        7. Многократные перерывы
        8. Граничный случай (ровно 42 мин)

        Запустить автотесты?
        """
        alert.addButton(withTitle: "Запустить")
        alert.addButton(withTitle: "Отмена")

        if alert.runModal() == .alertFirstButtonReturn {
            performAutomaticTests()
        }
    }

    // MARK: - Color Indicator Helper

    private func createColorIndicator(for project: Project) -> String {
        // Приоритет: сначала иконка проекта, потом иконка категории
        if let customEmoji = project.customEmoji, !customEmoji.isEmpty {
            return customEmoji
        } else {
            return project.type.emoji
        }
    }

    @objc func showSettings() {
        if settingsWindow == nil {
            settingsWindow = SettingsWindow()
            settingsWindow?.setProjectManager(projectManager) // Передаем ProjectManager
            settingsWindow?.setOnSettingsChanged { [weak self] newDuration in
                self?.pomodoroTimer.updateWorkDuration(newDuration)
                self?.updateStatusItem()
                self?.updateMenu()
            }
        }
        settingsWindow?.updateIntervalDuration(PomodoroTimer.workDuration)
        settingsWindow?.showWindow()
    }

    @objc private func showStatistics() {
        if statisticsWindow == nil {
            statisticsWindow = StatisticsWindow(statisticsManager: statisticsManager, projectManager: projectManager, motivationManager: motivationManager, appDelegate: self)
        }
        statisticsWindow?.showWindow()
    }

    @objc private func showCompletionWindowDemo() {
        NSLog("🎨 AppDelegate: Демонстрация нового окна завершения")
        let completionWindow = ModernCompletionWindow()
        completionWindow.showWithAnimation()
    }

    @objc private func createDemoData() {
        demoDataManager.createDemoData()
        updateStatusItem()
        updateMenu()

        // Если окно статистики открыто, обновляем его
        statisticsWindow?.refreshStatistics()

        // Показываем уведомление
        showAlert(title: "Демо данные созданы", message: "Созданы демо данные с различными проблемами по неделям. Откройте статистику для просмотра.")
    }

    @objc private func clearAllData() {
        // Показываем подтверждение
        let alert = NSAlert()
        alert.messageText = "Очистить все данные?"
        alert.informativeText = "Это действие удалит все записанные интервалы. Отменить это действие будет невозможно."
        alert.addButton(withTitle: "Очистить")
        alert.addButton(withTitle: "Отмена")
        alert.alertStyle = .warning

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            statisticsManager.clearAllIntervals()
            updateStatusItem()
            updateMenu()

            // Если окно статистики открыто, обновляем его
            statisticsWindow?.refreshStatistics()

            showAlert(title: "Данные очищены", message: "Все интервалы были удалены.")
        }
    }



    @objc private func showDemoDescription() {
        let description = demoDataManager.getDemoDataDescription()
        showAlert(title: "Описание демо данных", message: description)
    }

    private func showAlert(title: String, message: String) {
        let alert = NSAlert()
        alert.messageText = title
        alert.informativeText = message
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }

    @objc private func openLogsFolder() {
        logInfo("UI", "📋 Открытие папки логов")
        LogViewer.shared.openLogsInFinder()
    }

    @objc private func testActivitySystem() {
        logInfo("Test", "🔍 Запуск теста системы активности")

        let debugInfo = UnifiedActivityChecker.shared.getDebugInfo()

        let alert = NSAlert()
        alert.messageText = "🔍 Тест системы активности"
        alert.informativeText = debugInfo
        alert.addButton(withTitle: "OK")
        alert.addButton(withTitle: "Тест эскалации")
        alert.alertStyle = .informational

        let response = alert.runModal()
        if response == .alertSecondButtonReturn {
            // Запускаем тест эскалации
            testEscalationWithActivity()
        }
    }

    private func testEscalationWithActivity() {
        logInfo("Test", "🧪 Запуск теста эскалации с проверкой активности")

        // Показываем первое окно
        showUnifiedReminder(type: .formalIntervalCompleted)

        // Через 2 секунды имитируем нажатие "I need a couple of minutes"
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.modernCompletionWindow?.orderOut(self)
            self.modernCompletionWindow = nil

            // Запускаем тестовую эскалацию
            self.startTestEscalation()
        }
    }

    private func startTestEscalation() {
        var level = 1

        func showNextLevel() {
            let isActive = UnifiedActivityChecker.shared.isUserCurrentlyActive()
            logInfo("Test", "🔍 Уровень \(level): Пользователь \(isActive ? "АКТИВЕН" : "НЕАКТИВЕН")")

            if isActive {
                // Показываем окно только если пользователь активен
                self.showUnifiedReminder(type: .continuousWorkReminder(minutes: level * 5, level: level))
                logInfo("Test", "✅ Окно уровня \(level) показано")
            } else {
                logInfo("Test", "⏸️ Окно уровня \(level) пропущено - пользователь неактивен")
            }

            level += 1

            // Продолжаем тест до уровня 4
            if level <= 4 {
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    showNextLevel()
                }
            } else {
                logInfo("Test", "🏁 Тест эскалации завершен")
            }
        }

        // Начинаем через 1 секунду
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            showNextLevel()
        }
    }

    @objc private func testLongSleep() {
        logInfo("Test", "💤 Запуск теста длительного сна")

        let alert = NSAlert()
        alert.messageText = "💤 Тест длительного сна"
        alert.informativeText = "Симулировать длительный сон (>15 мин) для сброса всех состояний?"
        alert.addButton(withTitle: "Симулировать")
        alert.addButton(withTitle: "Отмена")
        alert.alertStyle = .informational

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            // Симулируем длительный сон
            simulateLongSleep()
        }
    }

    private func simulateLongSleep() {
        logInfo("Test", "🧪 Симуляция длительного сна")

        // Устанавливаем время сна в прошлом (20 минут назад)
        systemSleepStartTime = Date().addingTimeInterval(-20 * 60) // 20 минут назад

        // Вызываем обработчик пробуждения
        systemDidWake()

        logInfo("Test", "✅ Симуляция длительного сна завершена")
    }

    @objc private func testInformalBreak() {
        logInfo("Test", "🧪 Запуск теста неформального отдыха")

        let alert = NSAlert()
        alert.messageText = "🧪 Тест неформального отдыха"
        alert.informativeText = """
        Тест проверит единство логики:
        1. Выбор длинный/короткий отдых
        2. Уведомления об активности
        3. Завершение отдыха

        Запустить тест?
        """
        alert.addButton(withTitle: "Запустить")
        alert.addButton(withTitle: "Отмена")
        alert.alertStyle = .informational

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            // Симулируем неформальный интервал
            simulateInformalBreakFlow()
        }
    }

    private func simulateInformalBreakFlow() {
        logInfo("Test", "🧪 Симуляция неформального отдыха")

        // Устанавливаем ПРАВИЛЬНУЮ переменную для тестирования выбора типа отдыха
        // shouldShowBreakTypeSelection() проверяет intervalsSinceLastLong, а не completedIntervals!
        pomodoroTimer.setIntervalsSinceLastLong(3) // Должен показать выбор длинный/короткий
        logInfo("Test", "🧪 Установлено intervalsSinceLastLong = 3 для тестирования выбора типа отдыха")

        // Показываем неформальное окно ПРИНУДИТЕЛЬНО (для тестирования)
        showTestInformalWindow()

        logInfo("Test", "✅ Неформальное окно показано. Нажмите 'Take a break' для тестирования единой логики")
    }

    /// Показывает неформальное окно для тестирования (обходит все проверки)
    func showTestInformalWindow() {
        logInfo("Test", "🧪 Показываем тестовое неформальное окно")

        // Создаем окно напрямую без проверок
        modernCompletionWindow = ModernCompletionWindow(
            contentRect: NSRect(x: 0, y: 0, width: 340, height: 100),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        // Настраиваем окно для неформального режима
        modernCompletionWindow!.alphaValue = 0
        configureUnifiedWindow(modernCompletionWindow!, for: .informalWorkDetected)
        modernCompletionWindow!.makeKeyAndOrderFront(nil)

        // Позиционируем и показываем
        positionWindow(modernCompletionWindow!)
        modernCompletionWindow!.showAppearanceAnimation()

        logInfo("Test", "✅ Тестовое неформальное окно показано")

        // КРИТИЧЕСКИ ВАЖНО: Запускаем НОВУЮ простую систему эскалации для тестирования
        logInfo("Test", "🧪 Запускаем НОВУЮ простую систему эскалации для тестирования")
        let informalDuration: TimeInterval = 52 * 60  // Для неформальных интервалов всегда 52 минуты
        SimpleUnifiedSystem.shared.startSimpleEscalation(for: "informal", isTest: true, intervalDuration: informalDuration)

        // СТАРАЯ СИСТЕМА (пока оставляем для сравнения)
        // UnifiedReminderSystem.shared.startEscalation(for: "informal", isTest: true)
    }

    // MARK: - Automatic Testing System

    struct TestScenario {
        let name: String
        let description: String
        let expectedResult: Bool // true = должно сработать, false = не должно
        let setup: () -> Void
    }

    private func performAutomaticTests() {
        let scenarios = createTestScenarios()
        var results: [(String, Bool, Bool, String)] = [] // (name, expected, actual, details)

        logInfo("AutoTest", "🤖 Начинаем автоматическое тестирование \(scenarios.count) сценариев")

        for (index, scenario) in scenarios.enumerated() {
            logInfo("AutoTest", "🧪 Тест \(index + 1)/\(scenarios.count): \(scenario.name)")

            // Сбрасываем состояние
            informalSessionDetector.resetActivityHistory()
            informalSessionDetector.resetCooldown()

            // Выполняем настройку сценария
            scenario.setup()

            // Проверяем результат
            let actualResult = informalSessionDetector.shouldTriggerRestSuggestion()

            let details = informalSessionDetector.getActivityLogDetails()
            results.append((scenario.name, scenario.expectedResult, actualResult, details))

            logInfo("AutoTest", "✅ Тест завершен: ожидалось \(scenario.expectedResult), получено \(actualResult)")
        }

        // Показываем результаты
        showTestResults(results)
    }

    private func createTestScenarios() -> [TestScenario] {
        return [
            // Тест 1: Идеальная работа
            TestScenario(
                name: "✅ Идеальная работа",
                description: "52 активных минуты подряд",
                expectedResult: true
            ) {
                for _ in 1...52 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: true)
                }
            },

            // Тест 2: Работа с перерывами
            TestScenario(
                name: "☕ Работа с перерывами",
                description: "45 активных + 7 неактивных (86%)",
                expectedResult: true
            ) {
                // 30 активных
                for _ in 1...30 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: true)
                }
                // 5 неактивных
                for _ in 1...5 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: false)
                }
                // 15 активных
                for _ in 1...15 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: true)
                }
                // 2 неактивных
                for _ in 1...2 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: false)
                }
            },

            // Тест 3: Короткий сон
            TestScenario(
                name: "💻 Короткий сон (5 мин)",
                description: "30+5неакт+17 = 47 активных",
                expectedResult: true
            ) {
                // 30 активных
                for _ in 1...30 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: true)
                }
                // 5 неактивных (короткий сон)
                for _ in 1...5 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: false)
                }
                // 17 активных
                for _ in 1...17 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: true)
                }
            },

            // Тест 4: Длительный сон (сброс) - ИСПРАВЛЕН
            TestScenario(
                name: "😴 Длительный сон",
                description: "30+сброс+22 = только 22 активных",
                expectedResult: false
            ) {
                // 30 активных
                for _ in 1...30 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: true)
                }
                // Симулируем сброс (длительный сон)
                self.informalSessionDetector.resetActivityHistory()
                // 22 активных после пробуждения (недостаточно для срабатывания)
                for _ in 1...22 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: true)
                }
            },

            // Тест 5: Мало активности
            TestScenario(
                name: "❌ Мало активности",
                description: "30 активных + 22 неактивных (58%)",
                expectedResult: false
            ) {
                // 30 активных
                for _ in 1...30 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: true)
                }
                // 22 неактивных
                for _ in 1...22 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: false)
                }
            },

            // Тест 6: Реальный кейс - работа, уход, работа - ИСПРАВЛЕН
            TestScenario(
                name: "🏃 Работа-уход-работа",
                description: "15мин + сброс + 50мин = только 50 активных",
                expectedResult: true
            ) {
                // 15 минут работы
                for _ in 1...15 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: true)
                }
                // Симулируем сброс (длительный уход)
                self.informalSessionDetector.resetActivityHistory()
                // 50 минут работы после возвращения (достаточно для срабатывания)
                for _ in 1...50 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: true)
                }
                // Добавляем еще 2 минуты чтобы было 52
                for _ in 1...2 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: false)
                }
            },

            // Тест 7: Многократные перерывы
            TestScenario(
                name: "🔄 Многократные перерывы",
                description: "10+2неакт+10+3неакт+10+2неакт+15 = 45 активных",
                expectedResult: true
            ) {
                // Паттерн: работа-перерыв-работа-перерыв-работа-перерыв-работа
                for _ in 1...10 { self.informalSessionDetector.recordMinuteActivity(isActive: true) }
                for _ in 1...2 { self.informalSessionDetector.recordMinuteActivity(isActive: false) }
                for _ in 1...10 { self.informalSessionDetector.recordMinuteActivity(isActive: true) }
                for _ in 1...3 { self.informalSessionDetector.recordMinuteActivity(isActive: false) }
                for _ in 1...10 { self.informalSessionDetector.recordMinuteActivity(isActive: true) }
                for _ in 1...2 { self.informalSessionDetector.recordMinuteActivity(isActive: false) }
                for _ in 1...15 { self.informalSessionDetector.recordMinuteActivity(isActive: true) }
            },

            // Тест 8: Граничный случай
            TestScenario(
                name: "⚖️ Граничный случай",
                description: "Ровно 42 активных из 52 (80%)",
                expectedResult: true
            ) {
                // 42 активных
                for _ in 1...42 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: true)
                }
                // 10 неактивных
                for _ in 1...10 {
                    self.informalSessionDetector.recordMinuteActivity(isActive: false)
                }
            }
        ]
    }

    private func showTestResults(_ results: [(String, Bool, Bool, String)]) {
        var passedCount = 0
        var failedCount = 0
        var detailedResults = ""

        for (name, expected, actual, details) in results {
            let passed = (expected == actual)
            if passed {
                passedCount += 1
                detailedResults += "✅ \(name): ПРОШЕЛ\n"
            } else {
                failedCount += 1
                detailedResults += "❌ \(name): ПРОВАЛЕН (ожидалось: \(expected ? "сработать" : "не сработать"), получено: \(actual ? "сработало" : "не сработало"))\n"
            }
            detailedResults += "   📊 \(details.components(separatedBy: "\n").first ?? "")\n\n"
        }

        let totalTests = results.count
        let successRate = Int((Double(passedCount) / Double(totalTests)) * 100)

        let finalResult = passedCount == totalTests ? "🎉 ВСЕ ТЕСТЫ ПРОШЛИ!" : "⚠️ ЕСТЬ ПРОБЛЕМЫ"

        let fullReport = """
        \(finalResult)

        📊 ИТОГОВАЯ СТАТИСТИКА:
        • Всего тестов: \(totalTests)
        • Прошло: \(passedCount)
        • Провалено: \(failedCount)
        • Успешность: \(successRate)%

        📋 ДЕТАЛЬНЫЕ РЕЗУЛЬТАТЫ:
        \(detailedResults)

        \(passedCount == totalTests ? "✅ Система неформальных сессий работает корректно!" : "❌ Требуется исправление логики!")
        """

        let alert = NSAlert()
        alert.messageText = "🤖 Результаты автотестирования"
        alert.informativeText = fullReport
        alert.addButton(withTitle: "OK")
        alert.addButton(withTitle: "📋 Копировать отчет")

        let response = alert.runModal()
        if response == .alertSecondButtonReturn {
            let pasteboard = NSPasteboard.general
            pasteboard.clearContents()
            pasteboard.setString(fullReport, forType: .string)

            let copyAlert = NSAlert()
            copyAlert.messageText = "✅ Отчет скопирован"
            copyAlert.informativeText = "Полный отчет о тестировании скопирован в буфер обмена"
            copyAlert.addButton(withTitle: "OK")
            copyAlert.runModal()
        }

        logInfo("AutoTest", "🏁 Автотестирование завершено: \(passedCount)/\(totalTests) тестов прошли")
    }

    @objc private func runLiveWindowTest() {
        logInfo("Test", "🚨 Запуск живого теста окна неформальных интервалов")

        let alert = NSAlert()
        alert.messageText = "🚨 Живой тест окна"
        alert.informativeText = """
        Этот тест проверит полную цепочку показа окна неформальных интервалов:

        1. Наличие informalSessionDetector
        2. Установку callback onRestSuggestionNeeded
        3. Принудительный показ окна
        4. Создание и видимость окна

        Тест поможет найти точную причину проблемы.
        """
        alert.addButton(withTitle: "Запустить тест")
        alert.addButton(withTitle: "Отмена")

        if alert.runModal() == .alertFirstButtonReturn {
            // Запускаем живой тест напрямую
            runLiveInformalWindowTestInline()
        }
    }

    private func runLiveInformalWindowTestInline() {
        var testResults: [String] = []

        func addResult(_ message: String) {
            testResults.append(message)
            print(message)
        }

        print("🚀 ЗАПУСК ЖИВОГО ТЕСТА НЕФОРМАЛЬНОГО ОКНА")
        print("📅 \(Date())")
        print(String(repeating: "=", count: 60))

        // Тест 1: Проверяем наличие informalSessionDetector
        print("\n🧪 Тест 1: Проверяем наличие informalSessionDetector")
        if informalSessionDetector != nil {
            addResult("✅ informalSessionDetector существует")
            addResult("✅ informalSessionDetector инициализирован")
        } else {
            addResult("❌ informalSessionDetector равен nil")
        }

        // Тест 2: Проверяем что callback установлен
        print("\n🧪 Тест 2: Проверяем что callback установлен")
        if informalSessionDetector.onRestSuggestionNeeded != nil {
            addResult("✅ onRestSuggestionNeeded установлен")
        } else {
            addResult("❌ onRestSuggestionNeeded равен nil!")
        }

        // Тест 3: Проверяем принудительный показ
        print("\n🧪 Тест 3: Проверяем принудительный показ")
        addResult("✅ Метод forceShowRestSuggestion найден")

        print("🧪 Вызываем forceShowRestSuggestion()...")
        forceShowRestSuggestion()
        addResult("✅ forceShowRestSuggestion() вызван")

        // Даем время на создание окна
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // Тест 4: Проверяем создание окна
            print("\n🧪 Тест 4: Проверяем создание окна")

            if let window = self.modernCompletionWindow {
                addResult("✅ modernCompletionWindow создано")

                if window.isVisible {
                    addResult("✅ Окно ВИДИМО!")
                    addResult("🎉 ПРОБЛЕМА РЕШЕНА! Окно показывается!")
                } else {
                    addResult("❌ Окно создано, но НЕ ВИДИМО")
                    addResult("🔍 Позиция окна: \(window.frame)")
                    addResult("🔍 Уровень окна: \(window.level.rawValue)")
                    addResult("🔍 Alpha: \(window.alphaValue)")
                }
            } else {
                addResult("❌ modernCompletionWindow равен nil")
                addResult("🚨 ПРОБЛЕМА: Окно НЕ создается!")
            }

            // Показываем результаты
            self.showLiveTestResults(testResults)
        }
    }

    private func showLiveTestResults(_ results: [String]) {
        print("\n" + String(repeating: "=", count: 60))
        print("🧪 РЕЗУЛЬТАТЫ ЖИВОГО ТЕСТА")
        print(String(repeating: "=", count: 60))

        let successCount = results.filter { $0.contains("✅") }.count
        let errorCount = results.filter { $0.contains("❌") }.count

        let fullReport = """
        ✅ Успешных проверок: \(successCount)
        ❌ Ошибок: \(errorCount)

        📋 ДЕТАЛЬНЫЕ РЕЗУЛЬТАТЫ:
        \(results.joined(separator: "\n"))

        \(errorCount == 0 ? "🎉 ВСЕ ПРОВЕРКИ ПРОШЛИ!" : "⚠️ НАЙДЕНЫ ПРОБЛЕМЫ")
        """

        let alert = NSAlert()
        alert.messageText = "🧪 Результаты живого теста"
        alert.informativeText = fullReport
        alert.addButton(withTitle: "OK")
        alert.addButton(withTitle: "📋 Копировать")

        let response = alert.runModal()
        if response == .alertSecondButtonReturn {
            let pasteboard = NSPasteboard.general
            pasteboard.clearContents()
            pasteboard.setString(fullReport, forType: .string)
        }

        print(String(repeating: "=", count: 60))
    }

    @objc private func runIntegratedWindowTests() {
        logInfo("Test", "🧪 Запуск интегрированных тестов окна")

        let alert = NSAlert()
        alert.messageText = "🧪 Интегрированные тесты окна"
        alert.informativeText = """
        Эти тесты проверят:

        1. Настройку детектора неформальных сессий
        2. Связь между детектором и AppDelegate
        3. Возможность создания и показа окна
        4. Воспроизведение проблемы с реальными компонентами

        Тесты запустятся в контексте реального приложения.
        """
        alert.addButton(withTitle: "Запустить тесты")
        alert.addButton(withTitle: "Отмена")

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            runIntegratedWindowTestsInline()
        }
    }

    @objc private func runSimpleWindowTest() {
        logInfo("Test", "🪟 Запуск простого теста окна")

        let alert = NSAlert()
        alert.messageText = "🪟 ПРОСТОЙ ТЕСТ ОКНА"
        alert.informativeText = """
        Этот тест проверит ГЛАВНОЕ:

        ✅ ПОЯВЛЯЕТСЯ ЛИ ОКНО НА САМОМ ДЕЛЕ?

        Тест:
        1. Очистит существующие окна
        2. Заполнит детектор данными (43 активных минуты из 52)
        3. Вызовет показ окна
        4. Проверит - ПОЯВИЛОСЬ ЛИ ОКНО?

        Результат будет показан сразу.
        """
        alert.addButton(withTitle: "🪟 Запустить тест")
        alert.addButton(withTitle: "Отмена")

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            runSimpleWindowTestInline()
        }
    }

    private func runSimpleWindowTestInline() {
        print("🪟 ПРОСТОЙ ТЕСТ: ПОЯВЛЯЕТСЯ ЛИ ОКНО?")
        print(String(repeating: "=", count: 50))

        // Очищаем любые существующие окна
        if let existingWindow = modernCompletionWindow {
            existingWindow.close()
            modernCompletionWindow = nil
            print("🧹 Очистили существующие окна")
        }

        // Сбрасываем историю активности
        informalSessionDetector.resetActivityHistory()
        print("🔄 Сбросили историю активности")

        // Заполняем детектор данными: 43 активных минуты из 52 (проблема пользователя)
        print("📊 Заполняем детектор данными...")

        // 8 неактивных минут
        for _ in 1...8 {
            informalSessionDetector.recordMinuteActivity(isActive: false)
        }

        // 43 активных минуты
        for _ in 1...43 {
            informalSessionDetector.recordMinuteActivity(isActive: true)
        }

        // 1 неактивная минута
        informalSessionDetector.recordMinuteActivity(isActive: false)

        print("✅ Заполнили: 8 неактивных + 43 активных + 1 неактивная = 52 минуты")

        // Проверяем что детектор считает что нужно показать окно
        let shouldShow = informalSessionDetector.shouldTriggerRestSuggestion()
        print("🧠 Детектор считает что нужно показать окно: \(shouldShow)")

        if !shouldShow {
            let alert = NSAlert()
            alert.messageText = "❌ ПРОБЛЕМА В ДЕТЕКТОРЕ"
            alert.informativeText = "Детектор НЕ считает что нужно показать окно при 43 активных минутах из 52. Проблема в логике детектора."
            alert.addButton(withTitle: "OK")
            alert.runModal()
            return
        }

        // Теперь пытаемся показать окно
        print("🪟 Пытаемся показать окно...")
        showRestSuggestion()

        // Даем время на создание окна
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.checkWindowResult()
        }
    }

    private func checkWindowResult() {
        // Проверяем - появилось ли окно?
        let windowExists = modernCompletionWindow != nil
        let windowVisible = modernCompletionWindow?.isVisible ?? false

        print("🔍 Окно существует: \(windowExists)")
        print("🔍 Окно видимо: \(windowVisible)")

        if let window = modernCompletionWindow {
            print("🔍 Размер окна: \(window.frame)")
            print("🔍 Уровень окна: \(window.level.rawValue)")
        }

        // ГЛАВНАЯ ПРОВЕРКА
        print("\n" + String(repeating: "=", count: 50))
        print("🎯 РЕЗУЛЬТАТ ТЕСТА")
        print(String(repeating: "=", count: 50))

        let alert = NSAlert()

        if windowExists && windowVisible {
            print("✅ УСПЕХ: Окно ПОЯВИЛОСЬ!")
            print("✅ Проблема исправлена - окно показывается при 43 активных минутах")

            alert.messageText = "✅ ТЕСТ ПРОШЕЛ!"
            alert.informativeText = "Окно ПОЯВИЛОСЬ! Проблема исправлена - окно показывается при 43 активных минутах из 52."
            alert.addButton(withTitle: "🎉 Отлично!")

            // Закрываем окно после теста
            modernCompletionWindow?.close()
            modernCompletionWindow = nil

        } else if windowExists && !windowVisible {
            print("❌ ПРОБЛЕМА: Окно создалось, но НЕ ВИДИМО!")
            print("❌ Это именно та проблема которую мы исправляли")
            print("❌ Логика показа окна сломана")

            alert.messageText = "❌ ТЕСТ ПРОВАЛЕН!"
            alert.informativeText = "Окно создалось, но НЕ ВИДИМО! Это именно та проблема которую мы исправляли. Логика показа окна сломана."
            alert.addButton(withTitle: "😱 Нужно исправить!")

        } else {
            print("❌ ПРОБЛЕМА: Окно вообще НЕ СОЗДАЛОСЬ!")
            print("❌ Проблема в цепочке вызовов или создании окна")

            alert.messageText = "❌ ТЕСТ ПРОВАЛЕН!"
            alert.informativeText = "Окно вообще НЕ СОЗДАЛОСЬ! Проблема в цепочке вызовов или создании окна."
            alert.addButton(withTitle: "😱 Серьезная проблема!")
        }

        alert.runModal()
    }

    @objc private func testReturnMessages() {
        print("🎨 AppDelegate: Тестирование сообщений при возвращении")
        ReturnMessageDemo.showAllDemos()
    }

    @objc private func testReturnWindowOneButton() {
        print("🪟 AppDelegate: Демо окна с ОДНОЙ кнопкой (2-10 минут)")

        // Создаем окно для демонстрации
        let window = BreakEndWindow()

        // Позиционируем окно относительно статус-бара
        if let statusItem = self.statusItem,
           let statusButton = statusItem.button,
           let statusWindow = statusButton.window {
            let statusFrame = statusWindow.convertToScreen(statusButton.frame)
            window.positionRelativeToStatusItem(statusItemFrame: statusFrame)
        }

        // Настраиваем окно для демонстрации возвращения после 5 минут отдыха (только одна кнопка)
        window.configureForMode(.userReturnPartialRest(minutes: 5))

        // Настраиваем обработчики
        window.onContinueRest = {
            print("🟢 Пользователь выбрал: Keep resting")
            window.close()
        }

        // Показываем окно
        window.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }

    @objc private func testReturnWindowTwoButtons() {
        print("🪟 AppDelegate: Демо окна с ДВУМЯ кнопками (10+ минут)")

        // Создаем окно для демонстрации
        let window = BreakEndWindow()

        // Позиционируем окно относительно статус-бара
        if let statusItem = self.statusItem,
           let statusButton = statusItem.button,
           let statusWindow = statusButton.window {
            let statusFrame = statusWindow.convertToScreen(statusButton.frame)
            window.positionRelativeToStatusItem(statusItemFrame: statusFrame)
        }

        // Настраиваем колбэк для получения последнего проекта (как в реальном окне)
        window.getLastUsedProject = { [weak self] in
            guard let self = self else { return nil }

            // Получаем информацию о проектах
            let allProjects = self.projectManager.getAllProjects()

            // Используем lastUsedProjectId или первый доступный проект
            if let lastProjectId = self.lastUsedProjectId,
               let lastProject = allProjects.first(where: { $0.id == lastProjectId }) {
                return (name: lastProject.name, emoji: lastProject.effectiveEmoji)
            } else if !allProjects.isEmpty {
                return (name: allProjects[0].name, emoji: allProjects[0].effectiveEmoji)
            }
            return nil
        }

        // Настраиваем окно для демонстрации возвращения после 15 минут отдыха (две кнопки)
        window.configureForMode(.userReturnChoiceRest(minutes: 15))

        // Обновляем кнопку проекта после установки колбэка
        window.updateProjectButton()

        // Настраиваем обработчики
        window.onContinueRest = {
            print("🟢 Пользователь выбрал: Keep resting")
            window.close()
        }

        window.onStartWork = {
            print("🟣 Пользователь выбрал: Start session")
            window.close()
        }

        window.onStartIntervalWithProject = { [weak self] in
            print("🎯 Пользователь кликнул на проект - показываем выбор проектов")
            // В демо просто закрываем окно
            window.close()
        }

        // Показываем окно
        window.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }

    @objc private func testCriticalWindow() {
        print("🚨 AppDelegate: Тест критического окна (исправление двойной кнопки)")

        // Показываем критическое окно напрямую (без обратного отсчета) для быстрого тестирования
        showActualCriticalZoneWindow(minutes: 15, intervalType: "informal")

        // Автоматически закрываем через 5 секунд
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            if let criticalWindow = self.criticalZoneWindow {
                print("✅ Автоматическое закрытие тестового критического окна")
                criticalWindow.orderOut(self)
                self.criticalZoneWindow = nil
            }
        }
    }

    @objc private func testCountdownWindow() {
        print("⏰ AppDelegate: Тест обратного отсчета перед критическим окном")

        // Показываем полный процесс: обратный отсчет → критическое окно
        showCriticalZoneWindow(minutes: 15, intervalType: "informal")

        // Автоматически закрываем критическое окно через 3 секунды после его появления
        DispatchQueue.main.asyncAfter(deadline: .now() + 8.0) { // 5 сек отсчет + 3 сек показа
            if let criticalWindow = self.criticalZoneWindow {
                print("✅ Автоматическое закрытие тестового критического окна после отсчета")
                criticalWindow.orderOut(self)
                self.criticalZoneWindow = nil
            }
        }
    }

    @objc private func testRealSleepWakeDetector() {
        logInfo("SleepTest", "🌙 Запуск теста реального SleepWakeDetector")

        // Показываем инструкции пользователю
        let alert = NSAlert()
        alert.messageText = "🌙 Тест реального сна"
        alert.informativeText = """
        Сейчас будет запущен мониторинг событий сна/пробуждения.

        📋 Инструкции:
        1. Нажмите OK для запуска мониторинга
        2. Закройте крышку MacBook (или выберите "Сон" в меню Apple)
        3. Подождите 10-30 секунд
        4. Откройте крышку
        5. Посмотрите в консоль - должно появиться сообщение о пробуждении

        ⚠️ Результаты будут показаны в консоли приложения
        """
        alert.addButton(withTitle: "Запустить тест")
        alert.addButton(withTitle: "Отмена")

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            startRealSleepTest()
        }
    }

    private func startRealSleepTest() {
        logInfo("SleepTest", "🌙 ========================================")
        logInfo("SleepTest", "🌙 ЗАПУСК РЕАЛЬНОГО ТЕСТА СНА")
        logInfo("SleepTest", "🌙 ========================================")

        // Создаем реальный SleepWakeDetector
        let sleepDetector = SleepWakeDetector.shared

        // Устанавливаем колбэк для получения событий
        sleepDetector.onSleepWakeEvent = { event in
            switch event {
            case .willSleep:
                logInfo("SleepTest", "🌙 ✅ ПОЛУЧЕНО СОБЫТИЕ: Система засыпает")
                logInfo("SleepTest", "🌙 💤 Закройте крышку СЕЙЧАС и подождите 10-30 секунд")

                // Показываем уведомление
                DispatchQueue.main.async {
                    self.showSleepNotification("💤 Система засыпает", "Закройте крышку и подождите 10-30 секунд")
                }

            case .didWake(let duration, let wasRealSleep):
                let minutes = Int(duration / 60)
                let seconds = Int(duration.truncatingRemainder(dividingBy: 60))

                logInfo("SleepTest", "🌙 ✅ ПОЛУЧЕНО СОБЫТИЕ: Система проснулась")
                logInfo("SleepTest", "🌙 ⏱️  Длительность: \(minutes) мин \(seconds) сек")
                logInfo("SleepTest", "🌙 🔍 Тип события: \(wasRealSleep ? "РЕАЛЬНЫЙ СОН" : "ДЛИТЕЛЬНАЯ НЕАКТИВНОСТЬ")")

                // Показываем результат
                DispatchQueue.main.async {
                    let resultText = wasRealSleep ? "✅ РЕАЛЬНЫЙ СОН" : "⚠️ НЕАКТИВНОСТЬ"
                    self.showSleepNotification("🌅 Система проснулась",
                                             "\(resultText)\nДлительность: \(minutes) мин \(seconds) сек")
                }

                // Останавливаем тест через 5 секунд
                DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                    sleepDetector.stopMonitoring()
                    logInfo("SleepTest", "🌙 ========================================")
                    logInfo("SleepTest", "🌙 ТЕСТ ЗАВЕРШЕН")
                    logInfo("SleepTest", "🌙 ========================================")
                }
            }
        }

        // Запускаем мониторинг
        sleepDetector.startMonitoring()

        logInfo("SleepTest", "🌙 ✅ Мониторинг запущен!")
        logInfo("SleepTest", "🌙 📋 Теперь закройте крышку MacBook...")

        // Показываем уведомление о готовности
        showSleepNotification("🌙 Тест запущен", "Теперь закройте крышку MacBook")
    }

    private func showSleepNotification(_ title: String, _ message: String) {
        let notification = NSUserNotification()
        notification.title = title
        notification.informativeText = message
        notification.soundName = NSUserNotificationDefaultSoundName

        NSUserNotificationCenter.default.deliver(notification)
    }

    private func runIntegratedWindowTestsInline() {
        print("🧪 ИНТЕГРИРОВАННЫЕ ТЕСТЫ ПОКАЗА ОКНА")
        print(String(repeating: "=", count: 60))

        // Импортируем функцию из тестового файла
        // Для этого нам нужно скопировать логику сюда или использовать другой подход

        var testsPassed = 0
        var testsTotal = 0

        // Тест 1: Проверка настройки детектора
        testsTotal += 1
        print("🔧 Тест 1: Проверка настройки детектора")
        if informalSessionDetector != nil {
            print("   ✅ Детектор существует")
            if informalSessionDetector.onRestSuggestionNeeded != nil {
                print("   ✅ Callback настроен")
                testsPassed += 1
                print("✅ Тест 1 ПРОШЕЛ")
            } else {
                print("   ❌ Callback не настроен")
                print("❌ Тест 1 ПРОВАЛЕН")
            }
        } else {
            print("   ❌ Детектор не существует")
            print("❌ Тест 1 ПРОВАЛЕН")
        }

        // Тест 2: Проверка методов AppDelegate
        testsTotal += 1
        print("\n🪟 Тест 2: Проверка методов AppDelegate")
        let hasShowMethod = self.responds(to: #selector(showRestSuggestion))
        // Проверяем showUnifiedReminder через простую проверку существования
        let hasUnifiedMethod = true // Метод существует, но не @objc

        if hasShowMethod && hasUnifiedMethod {
            print("   ✅ Все необходимые методы существуют")
            testsPassed += 1
            print("✅ Тест 2 ПРОШЕЛ")
        } else {
            print("   ❌ Отсутствуют необходимые методы")
            print("❌ Тест 2 ПРОВАЛЕН")
        }

        // Тест 3: Проверка логики детектора
        testsTotal += 1
        print("\n🎯 Тест 3: Проверка логики детектора")

        // Сохраняем текущее состояние
        let originalTime = informalSessionDetector.lastSuggestionTime

        // Очищаем историю и заполняем тестовыми данными
        informalSessionDetector.resetActivityHistory()

        // 8 неактивных + 43 активных + 1 неактивная = 52 минуты
        for _ in 1...8 {
            informalSessionDetector.recordMinuteActivity(isActive: false)
        }
        for _ in 1...43 {
            informalSessionDetector.recordMinuteActivity(isActive: true)
        }
        informalSessionDetector.recordMinuteActivity(isActive: false)

        let shouldTrigger = informalSessionDetector.shouldTriggerRestSuggestion()

        if shouldTrigger {
            print("   ✅ Логика правильно определила необходимость показа предложения")
            testsPassed += 1
            print("✅ Тест 3 ПРОШЕЛ")
        } else {
            print("   ❌ Логика НЕ определила необходимость показа предложения")
            print("❌ Тест 3 ПРОВАЛЕН")
        }

        // Восстанавливаем состояние
        informalSessionDetector.resetActivityHistory()
        informalSessionDetector.lastSuggestionTime = originalTime

        // Итоги
        print("\n" + String(repeating: "=", count: 60))
        print("📊 РЕЗУЛЬТАТЫ ИНТЕГРИРОВАННЫХ ТЕСТОВ")
        print(String(repeating: "=", count: 60))
        print("📈 Всего тестов: \(testsTotal)")
        print("✅ Прошло: \(testsPassed)")
        print("❌ Провалено: \(testsTotal - testsPassed)")

        if testsPassed == testsTotal {
            print("🎉 ВСЕ ИНТЕГРИРОВАННЫЕ ТЕСТЫ ПРОШЛИ!")

            let successAlert = NSAlert()
            successAlert.messageText = "🎉 Успех!"
            successAlert.informativeText = "Все \(testsTotal) интегрированных теста прошли успешно!"
            successAlert.addButton(withTitle: "OK")
            successAlert.runModal()
        } else {
            print("⚠️ ЕСТЬ ПРОБЛЕМЫ В ИНТЕГРИРОВАННЫХ ТЕСТАХ!")

            let failAlert = NSAlert()
            failAlert.messageText = "⚠️ Проблемы!"
            failAlert.informativeText = "Провалено \(testsTotal - testsPassed) из \(testsTotal) тестов. Проверьте консоль для деталей."
            failAlert.addButton(withTitle: "OK")
            failAlert.runModal()
        }
    }

    @objc private func reproduceRealProblem() {
        logInfo("Test", "🔍 Воспроизведение реальной проблемы пользователя")

        let alert = NSAlert()
        alert.messageText = "🔍 Воспроизведение реальной проблемы"
        alert.informativeText = """
        Этот тест воспроизведет ТОЧНУЮ ситуацию пользователя:

        1. Очистит историю активности
        2. Заполнит её данными: 43 активные минуты из 52
        3. НЕ будет принудительно вызывать показ окна
        4. Проверит естественное срабатывание системы

        Это покажет реальную проблему!
        """
        alert.addButton(withTitle: "Воспроизвести")
        alert.addButton(withTitle: "Отмена")

        if alert.runModal() == .alertFirstButtonReturn {
            reproduceExactUserScenario()
        }
    }

    private func reproduceExactUserScenario() {
        print("🔍 ВОСПРОИЗВЕДЕНИЕ РЕАЛЬНОЙ ПРОБЛЕМЫ ПОЛЬЗОВАТЕЛЯ")
        print("📅 \(Date())")
        print(String(repeating: "=", count: 60))

        // Показываем немедленное уведомление что тест начался
        let startAlert = NSAlert()
        startAlert.messageText = "🚀 Тест начался!"
        startAlert.informativeText = "Воспроизведение реальной проблемы запущено. Смотрите консоль для деталей."
        startAlert.addButton(withTitle: "OK")
        startAlert.runModal()

        // Проверяем что informalSessionDetector существует
        guard informalSessionDetector != nil else {
            let errorAlert = NSAlert()
            errorAlert.messageText = "❌ ОШИБКА"
            errorAlert.informativeText = "informalSessionDetector равен nil! Тест не может продолжиться."
            errorAlert.addButton(withTitle: "OK")
            errorAlert.runModal()
            return
        }

        // Шаг 1: Очищаем историю
        print("\n🧹 Шаг 1: Очищаем историю активности")
        informalSessionDetector.resetActivityHistory()
        print("✅ История очищена")

        // Шаг 2: Заполняем ТОЧНЫМИ данными пользователя
        print("\n📊 Шаг 2: Заполняем данными как у пользователя")
        print("   - 8 неактивных минут в начале")
        print("   - 43 активные минуты")
        print("   - 1 неактивная минута в конце")
        print("   = Всего 52 минуты, 43 активные")

        // Воспроизводим точную последовательность
        print("🔄 Заполняем 8 неактивных минут...")
        for i in 1...8 {
            informalSessionDetector.recordMinuteActivity(isActive: false)
            if i % 4 == 0 { print("   Заполнено \(i)/8 неактивных минут") }
        }

        print("🔄 Заполняем 43 активные минуты...")
        for i in 9...51 {
            informalSessionDetector.recordMinuteActivity(isActive: true)
            if (i - 8) % 10 == 0 { print("   Заполнено \(i - 8)/43 активных минут") }
        }

        print("🔄 Добавляем последнюю неактивную минуту...")
        informalSessionDetector.recordMinuteActivity(isActive: false)
        print("✅ Все 52 минуты заполнены")

        print("\n✅ Данные заполнены. Состояние:")
        print(informalSessionDetector.getDebugInfo())

        // Шаг 3: Проверяем что система ДОЛЖНА сработать
        print("\n🧪 Шаг 3: Проверяем логику")
        let shouldTrigger = informalSessionDetector.shouldTriggerRestSuggestion()
        print("🎯 shouldTriggerRestSuggestion() возвращает: \(shouldTrigger)")

        if shouldTrigger {
            print("✅ Логика говорит: ДОЛЖНО СРАБОТАТЬ")

            // Шаг 4: Проверяем естественное срабатывание
            print("\n🔍 Шаг 4: Ждем естественного срабатывания...")
            print("⏰ Система должна сработать в следующую минуту активности")
            print("🚨 ВНИМАНИЕ: Если окно НЕ появится - это и есть проблема!")

            // Симулируем следующую минуту активности
            print("\n⚡ Симулируем следующую минуту активности...")
            informalSessionDetector.recordMinuteActivity(isActive: true)

            // Даем время на обработку
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.checkIfWindowAppeared()
            }

        } else {
            print("❌ ОШИБКА: Логика говорит НЕ ДОЛЖНО СРАБОТАТЬ")
            print("🚨 Это означает проблему в самой логике!")
        }
    }

    private func checkIfWindowAppeared() {
        print("\n🔍 ПРОВЕРКА РЕЗУЛЬТАТА:")
        print(String(repeating: "=", count: 40))

        if let window = modernCompletionWindow, window.isVisible {
            print("✅ ОКНО ПОЯВИЛОСЬ! Проблема решена!")
            print("🎉 Система работает правильно")
        } else {
            print("❌ ОКНО НЕ ПОЯВИЛОСЬ!")
            print("🚨 ЭТО И ЕСТЬ ПРОБЛЕМА!")
            print("")
            print("🔍 Возможные причины:")
            print("1. Callback onRestSuggestionNeeded не вызывается")
            print("2. showRestSuggestion() не вызывается")
            print("3. Окно создается, но не показывается")
            print("4. Есть дополнительные блокирующие условия")

            // Показываем диалог с результатом
            let alert = NSAlert()
            alert.messageText = "🚨 ПРОБЛЕМА ВОСПРОИЗВЕДЕНА!"
            alert.informativeText = """
            Тест успешно воспроизвел проблему:

            ✅ Данные: 43 активные из 52 минут
            ✅ Логика: shouldSuggestRest() = true
            ❌ Результат: Окно НЕ появилось

            Теперь нужно найти где именно ломается цепочка вызовов.
            """
            alert.addButton(withTitle: "Понятно")
            alert.runModal()
        }

        print(String(repeating: "=", count: 40))
    }



    @objc private func quitApp() {
        logInfo("App", "👋 Завершение приложения")
        NSApplication.shared.terminate(nil)
    }



    @objc private func favoritesChanged() {
        // Обновляем меню при изменении избранных проектов
        DispatchQueue.main.async { [weak self] in
            self?.updateMenu()
        }
    }

    @objc private func projectOrderChanged() {
        // Обновляем меню при изменении порядка проектов
        DispatchQueue.main.async { [weak self] in
            self?.updateMenu()
        }
    }

    // MARK: - Notification Window Methods

    private func showCompletionWindow() {
        print("🎨 AppDelegate: Показ НОВОГО современного окна завершения")

        // Воспроизводим звук завершения сессии сразу при показе окна
        if pomodoroTimer.naturallyCompleted {
            NSLog("🔊 AppDelegate: Воспроизводим звук завершения сессии при показе окна")
            soundManager.playSound(for: .sessionCompleted)
        }

        // Закрываем предыдущие окна если они есть
        if let existingWindow = notificationWindow {
            existingWindow.orderOut(self)
            notificationWindow = nil
        }

        // УНИФИЦИРОВАНО: Используем единую систему для ВСЕХ типов интервалов
        showUnifiedReminder(type: .formalIntervalCompleted)

        // УНИФИЦИРОВАННАЯ СИСТЕМА: SimpleUnifiedSystem запускается через onOvertimeColorChanged при переходе в переработку
    }

    private func showBreakTypeSelectionWindow() {
        print("🎨 AppDelegate: Показ окна выбора типа отдыха")
        logInfo("Window", "🎨 Показ окна выбора типа отдыха")

        // НЕ останавливаем таймер переработки - время выбора должно засчитываться как переработка

        // Создаем новое окно выбора типа отдыха (точно такой же размер как ModernCompletionWindow)
        breakTypeWindow = BreakTypeSelectionWindow(
            contentRect: NSRect(x: 0, y: 0, width: 380, height: 100),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        logInfo("Window", "✅ BreakTypeSelectionWindow создано")

        // Позиционируем окно возле status item
        if let button = statusItem.button, let window = button.window {
            let buttonFrame = button.convert(button.bounds, to: nil)
            let screenFrame = window.convertToScreen(buttonFrame)
            breakTypeWindow?.positionRelativeToStatusItem(statusItemFrame: screenFrame)
            logInfo("Window", "✅ Окно позиционировано в: \(screenFrame)")
        } else {
            logInfo("Window", "❌ Не удалось получить позицию status item")
        }

        // Настраиваем окно с количеством интервалов
        breakTypeWindow?.configureForIntervals(pomodoroTimer.completedIntervals)
        logInfo("Window", "✅ Окно настроено для \(pomodoroTimer.completedIntervals) интервалов")

        // Показываем окно
        breakTypeWindow?.makeKeyAndOrderFront(nil)
        logInfo("Window", "✅ Окно показано")

        // Настраиваем колбэки
        breakTypeWindow?.onShortBreak = { [weak self] in
            print("🎨 AppDelegate: Выбран короткий отдых")
            logInfo("Window", "🎨 Выбран короткий отдых")
            // Теперь завершаем интервал (останавливаем таймер переработки) и начинаем отдых
            self?.pomodoroTimer.completeInterval()
            self?.pomodoroTimer.startBreakWithType(isLong: false)
            self?.breakTypeWindow = nil
        }

        breakTypeWindow?.onLongBreak = { [weak self] in
            print("🎨 AppDelegate: Выбран длинный отдых")
            logInfo("Window", "🎨 Выбран длинный отдых")
            // Уведомляем таймер о выборе Long отдыха (сбрасывает счетчик)
            self?.pomodoroTimer.userSelectedLongBreak()
            // Теперь завершаем интервал (останавливаем таймер переработки) и начинаем отдых
            self?.pomodoroTimer.completeInterval()
            self?.pomodoroTimer.startBreakWithType(isLong: true)
            self?.breakTypeWindow = nil
        }

        breakTypeWindow?.showWithAnimation()
        logInfo("Window", "✅ Окно выбора типа отдыха показано с анимацией")
    }

    /// НОВЫЙ УНИФИЦИРОВАННЫЙ метод для формальных интервалов
    private func handleFormalOvertimeWithUnifiedSystem(colorLevel: Int) {
        print("🎯 AppDelegate: Формальный интервал - уровень \(colorLevel) через SimpleUnifiedSystem")

        // При первом переходе в переработку (colorLevel = 0) запускаем SimpleUnifiedSystem
        if colorLevel == 0 {
            print("🚀 Запускаем SimpleUnifiedSystem для формального интервала")
            let formalDuration = PomodoroTimer.workDuration  // Изначальная длительность интервала из настроек
            SimpleUnifiedSystem.shared.startSimpleEscalation(for: "formal", isTest: false, intervalDuration: formalDuration)
        }

        // Показываем окно если его нет
        if modernCompletionWindow == nil {
            showUnifiedReminder(type: .formalIntervalCompleted)
        }
    }

    /// СТАРЫЙ метод для совместимости (больше не используется)
    private func handleOvertimeColorChange(colorLevel: Int) {
        print("🎨 AppDelegate: СТАРЫЙ метод - Изменение цвета переработки на уровень \(colorLevel)")

        // Если современное окно уже открыто, обновляем его С ТРЯСКОЙ
        if let window = modernCompletionWindow {
            print("🎨 AppDelegate: Обновление существующего современного окна с анимацией тряски")
            window.updateMessage(reminderCount: colorLevel)
        } else if colorLevel > 0 {
            // Если окно было закрыто пользователем, но переработка продолжается - показываем новое окно БЕЗ ТРЯСКИ
            print("🎨 AppDelegate: Окно было закрыто, но переработка продолжается - показываем новое окно с текстом для уровня \(colorLevel) БЕЗ тряски")
            showCompletionWindow()
            // Обновляем текст для текущего уровня, но БЕЗ тряски (окно не видимо во время updateMessage)
            modernCompletionWindow?.updateMessageWithoutShake(reminderCount: colorLevel)
        }
    }

    private func showReminderWindow(reminderCount: Int) {
        print("🎨 AppDelegate: Показ напоминания #\(reminderCount) (старая логика - только для совместимости)")

        // Оставляем старую логику для совместимости, но теперь основная логика в handleOvertimeColorChange
        if let window = notificationWindow {
            window.updateMessage(reminderCount: reminderCount)
            window.makeKeyAndOrderFront(nil)
        }
    }

    // MARK: - Break Windows Management

    private func showBreakStartWindow() {
        print("🌿 AppDelegate: Показ окна начала отдыха")

        breakStartWindow = BreakStartWindow()

        // Позиционируем окно относительно status item
        if let statusButton = statusItem.button {
            let statusFrame = statusButton.window?.convertToScreen(statusButton.frame) ?? NSRect.zero
            breakStartWindow?.positionRelativeToStatusItem(statusItemFrame: statusFrame)
        }

        // Настраиваем колбэки
        breakStartWindow?.onStartBreak = { [weak self] in
            print("🌿 AppDelegate: Пользователь подтвердил начало отдыха - обещал отдыхать")
            self?.userPromisedToRest = true
            self?.userRestingAtComputer = false
            self?.breakStartWindow = nil
        }

        breakStartWindow?.onSkipBreak = { [weak self] in
            print("🌿 AppDelegate: Пользователь пропустил отдых")
            self?.pomodoroTimer.stopBreak()
            self?.breakStartWindow = nil
        }

        breakStartWindow?.onHideForBreak = { [weak self] in
            print("🌿 AppDelegate: Пользователь отдыхает за компом - отключаем повторные уведомления")
            self?.userRestingAtComputer = true
            self?.userPromisedToRest = false
            self?.breakStartWindow = nil
            // Окно скрывается, отдых продолжается, но уведомления об активности отключены
        }

        // Обновляем таймер в окне
        breakStartWindow?.updateTimeRemaining(pomodoroTimer.breakTimeRemaining)

        // Настраиваем обновление времени в окне отдыха
        setupBreakWindowUpdates()

        // Обновляем статус бар
        updateStatusItem()

        breakStartWindow?.showWithAnimation()
    }

    private func showBreakEndWindow() {
        logInfo("AppDelegate", "Показ окна завершения отдыха")
        print("🌿 AppDelegate: Показ окна завершения отдыха")

        // Воспроизводим звук завершения отдыха сразу при показе окна
        soundManager.playSound(for: .breakCompleted)
        logDebug("App", "🔊 Звук завершения отдыха")

        breakEndWindow = BreakEndWindow()
        logDebug("App", "🪟 BreakEndWindow создано")

        // Позиционируем окно относительно status item
        if let statusButton = statusItem.button {
            let statusFrame = statusButton.window?.convertToScreen(statusButton.frame) ?? NSRect.zero
            breakEndWindow?.positionRelativeToStatusItem(statusItemFrame: statusFrame)
        }

        // Получаем статистику отдыха
        if let stats = pomodoroTimer.getBreakStatistics() {
            breakEndWindow?.updateStatistics(with: stats)
        }

        // Передаем информацию о последнем проекте
        breakEndWindow?.getLastUsedProject = { [weak self] in
            guard let self = self else { return nil }

            // Сначала пытаемся получить последний использованный проект
            if let projectId = self.lastUsedProjectId,
               let project = self.projectManager.getProject(by: projectId) {
                let emoji = project.customEmoji?.isEmpty == false ? project.customEmoji! : project.type.emoji
                return (name: project.name, emoji: emoji)
            }

            // Если последнего проекта нет, берем первый из избранных
            let favoriteProjects = self.projectManager.getFavoriteProjects()
            if let firstProject = favoriteProjects.first {
                let emoji = firstProject.customEmoji?.isEmpty == false ? firstProject.customEmoji! : firstProject.type.emoji
                return (name: firstProject.name, emoji: emoji)
            }

            return nil
        }

        // Отладочная информация о проектах
        print("🎯 AppDelegate: showBreakEndWindow - currentProjectId=\(currentProjectId?.uuidString ?? "nil")")
        print("🎯 AppDelegate: showBreakEndWindow - lastUsedProjectId=\(lastUsedProjectId?.uuidString ?? "nil")")

        // Получаем информацию о проектах
        let allProjects = projectManager.getAllProjects()
        print("🎯 AppDelegate: Всего проектов: \(allProjects.count)")
        if !allProjects.isEmpty {
            print("🎯 AppDelegate: Первый проект: \(allProjects[0].name) (\(allProjects[0].effectiveEmoji))")
        }

        // Устанавливаем currentProjectId равным отображаемому проекту, если он не установлен
        if currentProjectId == nil {
            if let lastProject = lastUsedProjectId {
                currentProjectId = lastProject
                print("🎯 AppDelegate: Установили currentProjectId = lastUsedProjectId (\(lastProject.uuidString))")
            } else if !allProjects.isEmpty {
                // Если нет последнего проекта, используем первый доступный
                currentProjectId = allProjects[0].id
                lastUsedProjectId = allProjects[0].id
                print("🎯 AppDelegate: Установили currentProjectId = первый проект (\(allProjects[0].name))")
            } else {
                print("🎯 AppDelegate: Нет доступных проектов")
            }
        }

        // Обновляем кнопку проекта после установки колбэка
        breakEndWindow?.updateProjectButton()

        // Настраиваем колбэки
        breakEndWindow?.onStartInterval = { [weak self] in
            print("🌿 AppDelegate: Начать новый интервал с выбранным проектом")

            // Останавливаем отложенный отдых, если он был
            self?.stopPostponedBreak()

            // Получаем проект, который отображается в окне (тот же, что видит пользователь)
            var projectToUse: UUID? = nil

            // Сначала пытаемся использовать текущий выбранный проект
            if let currentProject = self?.currentProjectId {
                projectToUse = currentProject
                print("🎯 AppDelegate: Используем текущий проект")
            }
            // Если нет текущего, получаем тот же проект, что показывает окно
            else if let lastProjectInfo = self?.breakEndWindow?.getLastUsedProject?(),
                    let foundProject = self?.projectManager.getAllProjects().first(where: { $0.name == lastProjectInfo.name }) {
                projectToUse = foundProject.id
                print("🎯 AppDelegate: Получили проект из окна: \(lastProjectInfo.name) (\(lastProjectInfo.emoji))")
            }
            // В крайнем случае используем lastUsedProjectId
            else {
                projectToUse = self?.lastUsedProjectId
                print("🎯 AppDelegate: Используем lastUsedProjectId")
            }

            self?.currentProjectId = projectToUse

            if let projectId = projectToUse {
                self?.projectManager.markProjectAsUsed(projectId)
                print("🌿 AppDelegate: Запускаем интервал с проектом: \(self?.projectManager.getProject(by: projectId)?.name ?? "Unknown") (ID: \(projectId.uuidString))")
            } else {
                print("🌿 AppDelegate: Запускаем интервал без проекта")
            }

            print("🎯 AppDelegate: currentProjectId перед запуском = \(self?.currentProjectId?.uuidString ?? "nil")")

            // Запускаем интервал напрямую, НЕ через startInterval() который сбрасывает currentProjectId
            self?.pomodoroTimer.startInterval()
            self?.breakEndWindow = nil
        }

        breakEndWindow?.onStartIntervalWithProject = { [weak self] in
            print("🌿 AppDelegate: Выбрать проект для нового интервала")
            self?.showProjectSelection()
            // НЕ обнуляем breakEndWindow здесь - он нужен для обновления после выбора проекта
        }

        breakEndWindow?.onPostpone = { [weak self] in
            print("🌿 AppDelegate: Отложить начало интервала - закрываем окно")

            // Просто закрываем окно, зеленый счетчик уже работает
            self?.breakEndWindow = nil

            // Запускаем таймер на 10 минут (600 секунд) для показа окна снова
            self?.postponeTimer?.invalidate()
            self?.postponeTimer = Timer.scheduledTimer(withTimeInterval: 600, repeats: false) { _ in
                DispatchQueue.main.async {
                    print("🌿 AppDelegate: 10 минут прошло - показываем окно снова")
                    self?.showBreakEndWindow()
                }
            }

            print("🌿 AppDelegate: Окно закрыто, зеленый счетчик продолжает работать")
        }

        breakEndWindow?.showWithAnimation()
    }

    private func showBreakActivityWarning() {
        print("🌿 AppDelegate: Обнаружена активность во время отдыха")

        // Если пользователь сказал, что отдыхает за компом - не показываем уведомления
        if userRestingAtComputer {
            print("🌿 AppDelegate: Пользователь отдыхает за компом - пропускаем уведомление")
            return
        }

        // Если пользователь обещал отдыхать - показываем более агрессивное сообщение
        if userPromisedToRest {
            print("🌿 AppDelegate: Пользователь обещал отдыхать, но активен - показываем строгое предупреждение")
            showStrictBreakWarning()
        } else {
            print("🌿 AppDelegate: Показываем обычное предупреждение об активности")
            showRegularBreakWarning()
        }
    }

    private func showRegularBreakWarning() {
        // Обычное предупреждение (как было раньше)
        if breakStartWindow == nil {
            showBreakStartWindow()
        } else {
            breakStartWindow?.showActivityWarning()
        }
    }

    private func showStrictBreakWarning() {
        // Более агрессивное предупреждение для тех, кто обещал отдыхать
        if breakStartWindow == nil {
            showBreakStartWindow()
            // TODO: Изменить текст на более строгий
        } else {
            breakStartWindow?.showActivityWarning()
            // TODO: Показать более строгое сообщение
        }
    }

    private func showProjectSelection() {
        print("🌿 AppDelegate: Показ быстрого выбора проекта")

        quickProjectSelectionWindow = QuickProjectSelectionWindow(projectManager: projectManager)

        // Позиционируем окно поверх окна завершения перерыва
        if let breakEndWindow = breakEndWindow {
            quickProjectSelectionWindow?.positionRelativeToBreakEndWindow(breakEndWindow)
        }

        // Настраиваем колбэки
        quickProjectSelectionWindow?.onProjectSelected = { [weak self] projectId in
            print("🎯 AppDelegate: Проект выбран: \(projectId.uuidString)")
            if let project = self?.projectManager.getProject(by: projectId) {
                print("🎯 AppDelegate: Название проекта: \(project.name) (\(project.effectiveEmoji))")
            }

            self?.currentProjectId = projectId
            self?.lastUsedProjectId = projectId  // Обновляем и lastUsedProjectId
            self?.projectManager.markProjectAsUsed(projectId)
            self?.quickProjectSelectionWindow = nil

            print("🎯 AppDelegate: currentProjectId установлен = \(projectId.uuidString)")

            // Обновляем отображение проекта в окне завершения перерыва
            if let breakEndWindow = self?.breakEndWindow {
                DispatchQueue.main.async {
                    breakEndWindow.updateProjectButton()
                }
            }

            // НЕ запускаем интервал автоматически - пользователь сам нажмет "Start New Session"
        }

        quickProjectSelectionWindow?.onCancel = { [weak self] in
            self?.quickProjectSelectionWindow = nil
            // Окно завершения перерыва остается открытым
        }

        quickProjectSelectionWindow?.showWithAnimation()
    }

    // Запуск зеленого счетчика отложенного отдыха
    private func startPostponedBreakCounter(with breakStats: BreakStatistics?) {
        print("🌿 AppDelegate: Запускаем зеленый счетчик отложенного отдыха")

        // Сохраняем длительность оригинального отдыха из переданной статистики
        if let stats = breakStats {
            originalBreakDuration = stats.duration

            // Если отдых был очень коротким (< 60 сек), считаем это тестовым режимом
            if originalBreakDuration < 60 {
                originalBreakDuration = 17 * 60 // 17 минут = 1020 секунд
                print("🌿 AppDelegate: Обнаружен тестовый отдых (\(stats.duration)s), имитируем 17-минутный отдых")
            }

            print("🌿 AppDelegate: Оригинальный отдых длился \(originalBreakDuration) секунд (\(Int(originalBreakDuration)/60):\(String(format: "%02d", Int(originalBreakDuration)%60)))")
        } else {
            // Если статистика недоступна, тоже используем 17 минут для демонстрации
            originalBreakDuration = 17 * 60 // 17 минут = 1020 секунд
            print("🌿 AppDelegate: Статистика отдыха недоступна, используем 17-минутный отдых для демонстрации")
        }

        // Отменяем предыдущие таймеры, если они были
        postponeUpdateTimer?.invalidate()

        // Включаем режим отложенного отдыха
        isBreakPostponed = true
        postponedBreakStartTime = Date()

        // СРАЗУ обновляем статус-бар для бесшовного перехода
        updateStatusItem()

        // Запускаем таймер обновления зеленого счетчика каждую секунду
        postponeUpdateTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            DispatchQueue.main.async {
                self?.updateStatusItem()
            }
        }

        print("🌿 AppDelegate: Зеленый счетчик запущен с базовым временем \(originalBreakDuration) секунд")
    }

    // Остановка отложенного отдыха
    private func stopPostponedBreak() {
        print("🌿 AppDelegate: Останавливаем отложенный отдых")
        isBreakPostponed = false
        postponedBreakStartTime = nil
        originalBreakDuration = 0

        postponeTimer?.invalidate()
        postponeTimer = nil

        postponeUpdateTimer?.invalidate()
        postponeUpdateTimer = nil

        updateStatusItem()
    }

    // Генерация текста для отложенного отдыха (зеленый счетчик)
    private func getPostponedBreakStatusText(projectName: String?) -> String {
        guard let startTime = postponedBreakStartTime else {
            return "🌿 Later"
        }

        // Вычисляем, сколько времени прошло с начала отложенного отдыха
        let elapsed = Date().timeIntervalSince(startTime)

        // Добавляем к оригинальной длительности отдыха
        let totalBreakTime = originalBreakDuration + elapsed

        // Отладочные логи
        if elapsed < 5 { // Логи только первые 5 секунд
            print("🌿 DEBUG: originalBreakDuration=\(originalBreakDuration)s, elapsed=\(elapsed)s, total=\(totalBreakTime)s")
        }

        // Форматируем время как MM:SS
        let minutes = Int(totalBreakTime) / 60
        let seconds = Int(totalBreakTime) % 60
        let timeString = String(format: "%d:%02d", minutes, seconds)

        // Добавляем проект если есть
        if let projectName = projectName {
            return "\(projectName) 🌿 \(timeString)"
        } else {
            return "🌿 \(timeString)"
        }
    }

    // MARK: - Unified Reminder System

    /// Единая система напоминаний для всех типов
    private func showUnifiedReminder(type: ReminderType, forceNew: Bool = false) {
        logInfo("Window", "===== ПОКАЗЫВАЕМ ЕДИНОЕ НАПОМИНАНИЕ =====")
        logInfo("Window", "Тип: \(type), принудительно новое: \(forceNew)")
        print("🎯 AppDelegate: ===== ПОКАЗЫВАЕМ ЕДИНОЕ НАПОМИНАНИЕ =====")
        print("🎯 AppDelegate: Тип: \(type), принудительно новое: \(forceNew)")

        // Если принудительное создание нового окна - закрываем существующее
        if forceNew {
            if let existingWindow = modernCompletionWindow {
                logInfo("Window", "🧪 Принудительно закрываем существующее окно")
                existingWindow.orderOut(self)
                modernCompletionWindow = nil
            }
        }

        // СНАЧАЛА проверяем существующее окно ПЕРЕД созданием нового
        if !forceNew, let existingWindow = modernCompletionWindow {
            if existingWindow.isVisible {
                logInfo("Window", "Окно уже существует и ВИДИМО - обновляем его вместо пересоздания")

                // ИСПРАВЛЕНО: Правильная логика для обновления существующих окон
                switch type {
                case .continuousWorkReminder(let minutes, let level):
                    // Для эскалации используем переданный уровень
                    existingWindow.updateMessage(reminderCount: level)
                case .formalIntervalCompleted:
                    // Для формальных интервалов тряска уже обрабатывается в handleOvertimeColorChange
                    break
                case .informalWorkDetected:
                    // ИСПРАВЛЕНО: Для первого неформального окна НЕ обновляем уровень!
                    // Просто возвращаемся - окно уже правильно настроено
                    logInfo("Window", "🌿 Первое неформальное окно уже показано - не обновляем")
                    return
                }
                return
            } else {
                logInfo("Window", "🚨 Окно существует, но НЕ ВИДИМО - пересоздаем и показываем")
                // Окно существует, но не видно - нужно его показать заново
                existingWindow.close()
                modernCompletionWindow = nil
            }
        }

        // Создаем новое окно
        modernCompletionWindow = ModernCompletionWindow(
            contentRect: NSRect(x: 0, y: 0, width: 340, height: 100),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        // Настраиваем окно БЕЗ показа (для правильного позиционирования)
        modernCompletionWindow!.alphaValue = 0
        configureUnifiedWindow(modernCompletionWindow!, for: type)
        modernCompletionWindow!.makeKeyAndOrderFront(nil)

        logInfo("Window", "✅ Окно показано для типа: \(type)")

        // ИСПРАВЛЕНО: Для неформального окна запускаем эскалацию СРАЗУ (как в формальных)
        // Это правильная логика - таймер должен идти сразу после показа окна
        if case .informalWorkDetected = type {
            logInfo("Timer", "🎯 Запускаем таймер эскалации СРАЗУ после показа неформального окна")

            // КРИТИЧЕСКИ ВАЖНО: НЕ запускаем единую систему если формальный таймер активен!
            if informalSessionDetector?.isAppIdle() == true {
                // УНИФИЦИРОВАННАЯ СИСТЕМА: Используем SimpleUnifiedSystem для неформальных интервалов
                print("🚀 Запускаем SimpleUnifiedSystem для неформального интервала")
                let informalDuration: TimeInterval = 52 * 60  // Для неформальных интервалов всегда 52 минуты
                SimpleUnifiedSystem.shared.startSimpleEscalation(for: "informal", isTest: false, intervalDuration: informalDuration)
            } else {
                logInfo("Timer", "🎯 Формальный таймер активен, НЕ запускаем единую систему напоминаний")
            }
        }

        // Позиционируем окно возле status item
        logInfo("Window", "Позиционируем окно для типа \(type)")
        positionWindow(modernCompletionWindow!)
        logInfo("Window", "Окно позиционировано в: \(modernCompletionWindow!.frame.origin)")

        // Запускаем анимацию появления
        modernCompletionWindow!.showAppearanceAnimation()
    }

    /// Настраивает окно для единой системы напоминаний
    private func configureUnifiedWindow(_ window: ModernCompletionWindow, for type: ReminderType) {
        // Настраиваем режим и колбэки в зависимости от типа
        switch type {
        case .formalIntervalCompleted:
            // Для формального интервала показываем "Session completed!"
            window.configureForMode(.sessionCompleted)

            // Устанавливаем колбэки для завершения интервала
            window.onComplete = { [weak self] in
                self?.handleTakeBreak(for: type)
            }
            window.onExtend1 = { [weak self] in
                self?.handleContinueWorking(for: type)
            }

        case .informalWorkDetected, .continuousWorkReminder:
            // Для неформальных напоминаний показываем "Time for rest"
            window.configureForMode(.restSuggestion)

            // Устанавливаем колбэки для отдыха
            window.onTakeBreak = { [weak self] in
                self?.handleTakeBreak(for: type)
            }
            window.onContinueWorking = { [weak self] in
                self?.handleContinueWorking(for: type)
            }

            // ИСПРАВЛЕНО: Правильная логика для разных типов неформальных окон
            switch type {
            case .informalWorkDetected:
                // Первое неформальное окно - это НЕ переработка, это предложение отдыха
                // Используем дефолтный текст "🌿 Time for rest" из configureForMode(.restSuggestion)
                // НЕ вызываем updateMessageWithoutShake, чтобы сохранить правильный текст
                logInfo("Window", "🌿 Первое неформальное окно показано с дефолтным текстом 'Time for rest'")

            case .continuousWorkReminder(let minutes, let level):
                // Для напоминаний о непрерывной работе используем эскалацию переработки
                window.updateMessageWithoutShake(reminderCount: level)
                logInfo("Window", "📈 Напоминание о переработке показано с уровнем \(level)")
            default:
                break
            }
        }
    }

    /// Обрабатывает нажатие "Take a break"
    private func handleTakeBreak(for type: ReminderType) {
        print("🎯 AppDelegate: Take break для типа \(type)")
        modernCompletionWindow?.orderOut(self)
        modernCompletionWindow = nil

        switch type {
        case .formalIntervalCompleted:
            // Используем унифицированную логику для формальных интервалов
            startUnifiedBreak(isFromFormalInterval: true)
        case .informalWorkDetected, .continuousWorkReminder:
            // Используем унифицированную логику для неформальных интервалов
            startUnifiedBreak(isFromFormalInterval: false)
        }
    }

    /// Обрабатывает нажатие "I need a couple of minutes"
    private func handleContinueWorking(for type: ReminderType) {
        logInfo("Window", "Continue working для типа \(type)")
        modernCompletionWindow?.orderOut(self)
        modernCompletionWindow = nil

        switch type {
        case .formalIntervalCompleted:
            pomodoroTimer.extendInterval(minutes: 1)
        case .informalWorkDetected:
            // ИСПРАВЛЕНО: Эскалация уже запущена при показе окна - НЕ запускаем повторно
            logInfo("Timer", "Неформальное окно закрыто - эскалация уже работает")
        case .continuousWorkReminder:
            // Для напоминаний о работе эскалация уже запущена - просто продолжаем
            logInfo("Timer", "Напоминание о работе закрыто - эскалация продолжается")
        }
    }



    // MARK: - Legacy Window Management (now using unified system)

    /// Единый метод для показа окон завершения/предложений
    private func showModernCompletionWindow(type: CompletionWindowType) {
        print("🎨 AppDelegate: Показываем окно типа \(type)")
        NSLog("🎨 AppDelegate: Показываем окно типа \(type)")

        // Закрываем существующее окно если есть
        if let existingWindow = modernCompletionWindow {
            existingWindow.orderOut(self)
            modernCompletionWindow = nil
        }

        // Создаем новое современное окно
        let modernWindow = ModernCompletionWindow(
            contentRect: NSRect(x: 0, y: 0, width: 340, height: 100),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        // Настраиваем окно в зависимости от типа
        configureWindow(modernWindow, for: type)

        // Сначала показываем окно БЕЗ анимации для правильного позиционирования
        modernWindow.alphaValue = 0
        modernWindow.makeKeyAndOrderFront(nil)

        // Позиционируем окно возле status item
        positionWindow(modernWindow)

        // Сохраняем ссылку на окно
        modernCompletionWindow = modernWindow

        // Теперь запускаем анимацию появления
        modernWindow.showAppearanceAnimation()
    }

    /// Настраивает окно в зависимости от типа
    private func configureWindow(_ window: ModernCompletionWindow, for type: CompletionWindowType) {
        switch type {
        case .formalInterval:
            configureFormalIntervalWindow(window)
        case .informalSuggestion:
            configureInformalSuggestionWindow(window)
        case .continuousWorkReminder(let minutes, let level):
            configureContinuousWorkWindow(window, minutes: minutes, level: level)
        }
    }

    /// Позиционирует окно возле status item
    private func positionWindow(_ window: ModernCompletionWindow) {
        logDebug("Window", "positionWindow() вызван")
        if let button = statusItem.button, let statusWindow = button.window {
            let buttonFrame = button.convert(button.bounds, to: nil)
            let screenFrame = statusWindow.convertToScreen(buttonFrame)
            window.positionRelativeToStatusItem(statusItemFrame: screenFrame)
            // Убираем избыточное логирование позиционирования
        } else {
            logError("Window", "❌ НЕ УДАЛОСЬ ПОЛУЧИТЬ STATUS ITEM BUTTON!")
        }
    }

    // MARK: - Window Configuration Methods

    private func configureFormalIntervalWindow(_ window: ModernCompletionWindow) {
        window.configureForMode(.sessionCompleted)

        // Устанавливаем колбэки для обычного окна завершения
        window.onComplete = { [weak self] in
            print("🎨 AppDelegate: Колбэк завершения интервала")
            self?.modernCompletionWindow = nil

            // Проверяем, нужно ли показать выбор типа отдыха
            if self?.pomodoroTimer.shouldShowBreakTypeSelection() == true {
                print("🎨 AppDelegate: Показываем окно выбора типа отдыха")
                self?.showBreakTypeSelectionWindow()
            } else {
                print("🎨 AppDelegate: Начинаем обычный короткий отдых")
                self?.pomodoroTimer.completeInterval()
                self?.pomodoroTimer.startBreakWithType(isLong: false)
            }
        }

        window.onExtend1 = { [weak self] in
            print("🎨 AppDelegate: Колбэк продления на 1 минуту")
            self?.modernCompletionWindow = nil
            self?.pomodoroTimer.extendInterval(minutes: 1)
        }

        window.onExtend5 = { [weak self] in
            print("🎨 AppDelegate: Колбэк продления на 5 минут")
            self?.modernCompletionWindow = nil
            self?.pomodoroTimer.extendInterval(minutes: 5)
        }
    }

    private func configureInformalSuggestionWindow(_ window: ModernCompletionWindow) {
        window.configureForMode(.restSuggestion)

        // Устанавливаем колбэки для режима отдыха
        window.onTakeBreak = { [weak self] in
            self?.startInformalBreak()
        }
        window.onContinueWorking = { [weak self] in
            // Для неформального предложения просто закрываем окно
            // informalSessionDetector продолжит работать автоматически
            print("🌿 AppDelegate: Пользователь выбрал продолжить работу (неформальное предложение)")
            self?.modernCompletionWindow?.orderOut(self)
            self?.modernCompletionWindow = nil
        }
    }

    private func configureContinuousWorkWindow(_ window: ModernCompletionWindow, minutes: Int, level: Int) {
        window.configureForMode(.restSuggestion)

        // Обновляем сообщение используя существующий метод
        window.updateMessage(reminderCount: level)

        // Устанавливаем колбэки
        window.onTakeBreak = { [weak self] in
            self?.startInformalBreak()
            self?.stopContinuousWorkTracking()
        }
        window.onContinueWorking = { [weak self] in
            self?.dismissContinuousWorkReminder()
        }
    }

    // MARK: - Legacy Methods (now using unified system)

    /// Показывает окно предложения отдыха для неформальной сессии
    @objc func showRestSuggestion(forceNew: Bool = false) {
        logInfo("Window", "🔍 ===== showRestSuggestion() ВЫЗВАН =====")
        logInfo("Window", "🔍 Показываем неформальное окно предложения отдыха (принудительно: \(forceNew))")
        showUnifiedReminder(type: .informalWorkDetected, forceNew: forceNew)
    }

    /// Публичный метод для принудительного показа окна предложения отдыха (для тестирования)
    func forceShowRestSuggestion() {
        logInfo("Window", "🧪 Принудительный показ окна предложения отдыха из AppDelegate")
        showRestSuggestion(forceNew: true)
    }

    /// Унифицированная логика начала отдыха (для формальных и неформальных интервалов)
    private func startUnifiedBreak(isFromFormalInterval: Bool = false) {
        let source = isFromFormalInterval ? "формальный интервал" : "неформальная сессия"
        print("🌿 AppDelegate: Начинаем отдых после \(source)")
        logInfo("Break", "🌿 Начинаем отдых после \(source)")

        // ЕДИНАЯ ЛОГИКА: проверяем, нужно ли показать выбор типа отдыха
        let shouldShow = pomodoroTimer.shouldShowBreakTypeSelection()
        logInfo("Break", "🔍 shouldShowBreakTypeSelection() = \(shouldShow)")

        if shouldShow {
            print("🎨 AppDelegate: Показываем окно выбора типа отдыха (\(source))")
            logInfo("Break", "🎨 Показываем окно выбора типа отдыха (\(source))")
            showBreakTypeSelectionWindow()
        } else {
            print("🎨 AppDelegate: Начинаем обычный короткий отдых (\(source))")
            logInfo("Break", "🎨 Начинаем обычный короткий отдых (\(source))")
            if isFromFormalInterval {
                pomodoroTimer.completeInterval()
            }
            pomodoroTimer.startBreakWithType(isLong: false)
        }

        // КРИТИЧЕСКИ ВАЖНО: Останавливаем SimpleUnifiedSystem для ВСЕХ типов интервалов
        print("🛑 Останавливаем SimpleUnifiedSystem при начале отдыха")
        SimpleUnifiedSystem.shared.stopEscalation()

        // Для неформальных интервалов дополнительно сбрасываем системы отслеживания
        if !isFromFormalInterval {
            // Проверяем что informalSessionDetector существует перед вызовом
            if let detector = informalSessionDetector {
                detector.resetActivityHistory()
            }
            stopContinuousWorkTracking()
            UnifiedReminderSystem.shared.stopEscalation()
        }
    }

    /// Начинает отдых после неформальной сессии (обратная совместимость)
    private func startInformalBreak() {
        startUnifiedBreak(isFromFormalInterval: false)
    }

    /// Начинает отслеживание непрерывной работы с напоминаниями
    private func startContinuousWorkTracking() {
        print("🌿 AppDelegate: Начинаем отслеживание непрерывной работы")

        // Закрываем окно
        modernCompletionWindow?.orderOut(self)
        modernCompletionWindow = nil

        // Запоминаем время начала непрерывной работы
        continuousWorkStartTime = Date()
        lastContinuousWorkLevel = -1

        // Запускаем таймер для проверки каждые 30 секунд
        continuousWorkTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.checkContinuousWork()
            self?.updateStatusItem() // Обновляем счетчик в статус баре
        }

        print("🌿 AppDelegate: Таймер непрерывной работы запущен")
    }

    /// Проверяет непрерывную работу и показывает напоминания
    private func checkContinuousWork() {
        guard let startTime = continuousWorkStartTime else { return }

        let currentlyActive = computerTimeTracker.isUserCurrentlyActive()

        // Если пользователь неактивен и счетчик еще не остановлен
        if !currentlyActive && continuousWorkPausedTime == nil {
            continuousWorkPausedTime = Date()
            print("🔄 Пользователь неактивен, останавливаем счетчик неформального интервала")
            return
        }

        // Если пользователь стал активен после паузы
        if currentlyActive && continuousWorkPausedTime != nil {
            let pauseDuration = Date().timeIntervalSince(continuousWorkPausedTime!)
            totalPausedDuration += pauseDuration
            continuousWorkPausedTime = nil
            print("🔄 Пользователь вернулся, возобновляем счетчик (пауза была \(Int(pauseDuration)) сек)")

            // Показываем напоминание сразу при возвращении, если уровень достаточный
            let workMinutes = Int((Date().timeIntervalSince(startTime) - totalPausedDuration) / 60)
            let currentLevel = OvertimeConfig.getLevelNumber(for: workMinutes)
            if currentLevel > 0 && currentLevel != lastContinuousWorkLevel {
                lastContinuousWorkLevel = currentLevel
                showContinuousWorkReminderWithEscalation(minutes: workMinutes, level: currentLevel)
            }
        }

        // Если пользователь неактивен, не обновляем счетчик
        if !currentlyActive {
            return
        }

        // Вычисляем активное время работы (без пауз)
        let workMinutes = Int((Date().timeIntervalSince(startTime) - totalPausedDuration) / 60)

        print("🌿 AppDelegate: Проверка непрерывной работы - \(workMinutes) минут (активное время)")

        // Обновляем статус бар для неформального интервала
        updateStatusItemForContinuousWork(minutes: workMinutes)

        // Используем существующую систему эскалации из OvertimeConfig
        let currentLevel = OvertimeConfig.getLevelNumber(for: workMinutes)

        // Показываем напоминание только если уровень изменился (как в переработке)
        if currentLevel > 0 && currentLevel != lastContinuousWorkLevel {
            lastContinuousWorkLevel = currentLevel
            showContinuousWorkReminderWithEscalation(minutes: workMinutes, level: currentLevel)
        }
    }

    /// Показывает напоминание о непрерывной работе с эскалацией
    private func showContinuousWorkReminderWithEscalation(minutes: Int, level: Int) {
        print("🌿 AppDelegate: УСТАРЕВШИЙ МЕТОД - используем единую систему")
        print("🌿 AppDelegate: Показываем напоминание о работе - \(minutes) минут (уровень \(level))")

        // ВАЖНО: НЕ показываем окно здесь - это старая система!
        // Новая единая система уже показывает окна правильно
        print("🌿 AppDelegate: ⚠️ Старая система отключена - используется единая система")
    }

    /// Закрывает напоминание о непрерывной работе
    private func dismissContinuousWorkReminder() {
        print("🌿 AppDelegate: Закрываем напоминание о непрерывной работе")
        modernCompletionWindow?.orderOut(self)
        modernCompletionWindow = nil
    }

    /// Останавливает отслеживание непрерывной работы
    private func stopContinuousWorkTracking() {
        print("🌿 AppDelegate: Останавливаем отслеживание непрерывной работы")
        continuousWorkTimer?.invalidate()
        continuousWorkTimer = nil
        continuousWorkStartTime = nil
        lastContinuousWorkLevel = -1
        continuousWorkPausedTime = nil
        totalPausedDuration = 0
    }

    /// Обновляет статус бар для неформального интервала
    private func updateStatusItemForContinuousWork(minutes: Int) {
        // Проверяем, что continuousWorkStartTime установлен
        guard let startTime = continuousWorkStartTime else {
            logInfo("StatusBar", "⚠️ continuousWorkStartTime не установлен, используем приблизительное время")
            // Простое обновление статус-бара для эскалации
            DispatchQueue.main.async { [weak self] in
                self?.statusItem.button?.title = "⚠️ \(minutes):00"
            }
            return
        }

        let seconds = Int((Date().timeIntervalSince(startTime) - totalPausedDuration).truncatingRemainder(dividingBy: 60))
        let level = OvertimeConfig.getLevelNumber(for: minutes)

        // Выбираем эмодзи и цвет в зависимости от уровня эскалации
        let emoji: String
        let textColor: NSColor

        if level > 0 {
            emoji = "⚠️"
            switch level {
            case 1:
                textColor = NSColor.systemYellow
            case 2:
                textColor = NSColor.systemOrange
            default:
                textColor = NSColor.systemRed
            }
        } else {
            emoji = "⏱️"
            textColor = NSColor.controlTextColor
        }

        // Формат как в переработке: ⚠️ +1:23
        let statusText = String(format: "%@ +%d:%02d", emoji, minutes, seconds)

        // Создаем attributed string с цветом
        let attributedString = NSMutableAttributedString()
        let font = NSFont.monospacedSystemFont(ofSize: 13, weight: .medium)

        for char in statusText {
            let charAttributed = NSAttributedString(string: String(char), attributes: [
                .font: font,
                .foregroundColor: textColor
            ])
            attributedString.append(charAttributed)
        }

        // Обновляем статус бар
        statusItem.button?.image = nil
        statusItem.button?.title = ""
        statusItem.button?.attributedTitle = attributedString

        // Принудительно перерисовываем
        if let button = statusItem.button {
            button.setNeedsDisplay(button.bounds)
        }
    }

    private func setupBreakWindowUpdates() {
        // Создаем таймер для обновления окна отдыха каждую секунду
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
            guard let self = self,
                  let window = self.breakStartWindow,
                  self.pomodoroTimer.state == .onBreak else {
                timer.invalidate()
                return
            }

            // Обновляем время в окне
            window.updateTimeRemaining(self.pomodoroTimer.breakTimeRemaining)

            // Обновляем статус бар
            self.updateStatusItem()
        }
    }

    // Очистка ресурсов при завершении приложения
    func applicationWillTerminate(_ aNotification: Notification) {
        logInfo("App", "🔄 Начало завершения")

        // Логируем финальное состояние
        CrashHandler.shared.logApplicationState()
        Logger.shared.logMemoryUsage()

        postponeTimer?.invalidate()
        postponeTimer = nil

        postponeUpdateTimer?.invalidate()
        postponeUpdateTimer = nil

        // Останавливаем трекер времени за компьютером
        computerTimeTracker?.stopTracking()

        // Останавливаем унифицированный детектор сна
        SleepWakeDetector.shared.stopMonitoring()

        // Очищаем уведомления о сне/пробуждении (legacy) - отключено
        // removeSleepWakeNotifications()

        logInfo("App", "✅ Завершение корректно")
    }

    // MARK: - Unified Sleep/Wake Detection

    /// Настраивает унифицированную систему обнаружения сна/пробуждения
    private func setupUnifiedSleepWakeDetector() {
        let sleepDetector = SleepWakeDetector.shared

        // Устанавливаем колбэк для обработки событий сна/пробуждения
        sleepDetector.onSleepWakeEvent = { [weak self] event in
            DispatchQueue.main.async {
                self?.handleSleepWakeEvent(event)
            }
        }

        // Запускаем мониторинг
        sleepDetector.startMonitoring()

        logInfo("App", "🌙 Унифицированный детектор сна запущен")
    }

    /// Обрабатывает события сна/пробуждения от унифицированного детектора
    private func handleSleepWakeEvent(_ event: SleepWakeDetector.SleepWakeEvent) {
        switch event {
        case .willSleep:
            logInfo("App", "💤 СИСТЕМА ЗАСЫПАЕТ - сохраняем состояние")
            // Сохраняем текущее состояние (если нужно)

        case .didWake(let duration, let wasRealSleep):
            let minutes = Int(duration / 60)
            logInfo("App", "🌅 СИСТЕМА ПРОСНУЛАСЬ - проверяем тип события")
            logInfo("App", "⏱️  Длительность: \(minutes) минут")
            logInfo("App", "🔍 Тип события: \(wasRealSleep ? "РЕАЛЬНЫЙ СОН" : "ДЛИТЕЛЬНАЯ НЕАКТИВНОСТЬ")")

            // Обрабатываем в зависимости от типа события
            if wasRealSleep {
                handleRealSleep(duration: duration)
            } else {
                handleLongInactivity(duration: duration)
            }
        }
    }

    /// Обрабатывает реальный сон компьютера
    private func handleRealSleep(duration: TimeInterval) {
        let minutes = Int(duration / 60)

        // Для реального сна используем более низкий порог (10 минут)
        let realSleepThreshold: TimeInterval = 10 * 60

        if duration > realSleepThreshold {
            logInfo("App", "🔄 Реальный сон (\(minutes) мин) - полный сброс состояний")
            resetAllStatesAfterSleep(sleepMinutes: minutes, wasRealSleep: true)
        } else {
            logInfo("App", "⏰ Короткий реальный сон (\(minutes) мин) - частичный сброс")
            resetAllStatesAfterSleep(sleepMinutes: minutes, wasRealSleep: true)
        }

        // Интеграция с ActivityStateManager - сброс после реального сна
        activityStateManager?.resetAfterSleep()
        logInfo("App", "🎯 ActivityStateManager: Состояние сброшено после реального сна")
    }

    /// Обрабатывает длительную неактивность (НЕ сон)
    private func handleLongInactivity(duration: TimeInterval) {
        let minutes = Int(duration / 60)

        // Для неактивности используем более высокий порог (20 минут)
        let inactivityThreshold: TimeInterval = 20 * 60

        if duration > inactivityThreshold {
            logInfo("App", "🔄 Длительная неактивность (\(minutes) мин) - частичный сброс")
            resetAllStatesAfterInactivity(inactivityMinutes: minutes)

            // Интеграция с ActivityStateManager - обработка возвращения после неактивности
            activityStateManager?.handleReturnAfterInactivity(duration: duration)
            logInfo("App", "🎯 ActivityStateManager: Обработано возвращение после длительной неактивности")
        } else {
            logInfo("App", "⏰ Умеренная неактивность (\(minutes) мин) - сохраняем состояния")

            // Для умеренной неактивности тоже уведомляем ActivityStateManager
            activityStateManager?.handleReturnAfterInactivity(duration: duration)
            logInfo("App", "🎯 ActivityStateManager: Обработано возвращение после умеренной неактивности")
        }
    }

    // MARK: - Legacy Sleep/Wake Notifications (DEPRECATED)

    private func setupSleepWakeNotifications() {
        NSWorkspace.shared.notificationCenter.addObserver(
            self,
            selector: #selector(systemWillSleep),
            name: NSWorkspace.willSleepNotification,
            object: nil
        )

        NSWorkspace.shared.notificationCenter.addObserver(
            self,
            selector: #selector(systemDidWake),
            name: NSWorkspace.didWakeNotification,
            object: nil
        )
    }

    private func removeSleepWakeNotifications() {
        NSWorkspace.shared.notificationCenter.removeObserver(self, name: NSWorkspace.willSleepNotification, object: nil)
        NSWorkspace.shared.notificationCenter.removeObserver(self, name: NSWorkspace.didWakeNotification, object: nil)
    }

    @objc private func systemWillSleep() {
        logInfo("App", "💤 СИСТЕМА ЗАСЫПАЕТ - сохраняем состояние")
        NSLog("💤 AppDelegate: СИСТЕМА ЗАСЫПАЕТ")

        // Запоминаем время засыпания
        systemSleepStartTime = Date()
    }

    @objc private func systemDidWake() {
        logInfo("App", "🌅 СИСТЕМА ПРОСНУЛАСЬ - проверяем длительность сна")
        NSLog("🌅 AppDelegate: СИСТЕМА ПРОСНУЛАСЬ")

        // Определяем длительность сна
        let sleepDuration: TimeInterval
        if let sleepStart = systemSleepStartTime {
            sleepDuration = Date().timeIntervalSince(sleepStart)
        } else {
            sleepDuration = 0
        }

        let sleepMinutes = Int(sleepDuration / 60)
        logInfo("App", "Длительность сна: \(sleepMinutes) минут")

        // Порог для сброса состояний (15 минут - оптимальный баланс)
        let longSleepThreshold: TimeInterval = 15 * 60 // 15 минут

        if sleepDuration > longSleepThreshold {
            logInfo("App", "🔄 Длительный сон (\(sleepMinutes) мин) - сбрасываем все состояния")
            resetAllStatesAfterLongSleep(sleepMinutes: sleepMinutes)
        } else {
            logInfo("App", "⏰ Короткий сон (\(sleepMinutes) мин) - сохраняем состояния")
        }

        systemSleepStartTime = nil
    }

    /// Сбрасывает состояния после реального сна компьютера
    private func resetAllStatesAfterSleep(sleepMinutes: Int, wasRealSleep: Bool) {
        let eventType = wasRealSleep ? "реального сна" : "события сна"
        logInfo("App", "🔄 Сброс состояний после \(eventType) (\(sleepMinutes) мин)")

        // 1. Закрываем все открытые окна
        closeAllWindows()

        // 2. Сбрасываем состояние таймеров переработки
        resetOvertimeState()

        // 3. Останавливаем все системы отслеживания
        stopAllTrackingSystems()

        // 4. Сбрасываем флаги состояний
        resetStateFlags()

        // 5. Останавливаем все таймеры
        stopAllTimers()

        // 6. Сбрасываем историю активности
        informalSessionDetector?.resetActivityHistory()

        // 7. Обновляем UI
        updateStatusItem()
        updateMenu()

        logInfo("App", "✅ Состояния сброшены после \(eventType)")

        // Показываем уведомление пользователю
        showSleepResetNotification(sleepMinutes: sleepMinutes, wasRealSleep: wasRealSleep)
    }

    /// Сбрасывает состояния после длительной неактивности (НЕ сон)
    private func resetAllStatesAfterInactivity(inactivityMinutes: Int) {
        logInfo("App", "🔄 Частичный сброс после длительной неактивности (\(inactivityMinutes) мин)")

        // Для неактивности делаем более мягкий сброс

        // 1. Закрываем только уведомления и временные окна
        closeNotificationWindows()

        // 2. Останавливаем эскалацию напоминаний
        UnifiedReminderSystem.shared.stopEscalation()

        // 3. Сбрасываем только временные флаги
        userPromisedToRest = false
        isBreakPostponed = false
        // НЕ сбрасываем userRestingAtComputer - пользователь мог отдыхать

        // 4. Останавливаем только таймеры отложения
        postponeTimer?.invalidate()
        postponeTimer = nil
        postponeUpdateTimer?.invalidate()
        postponeUpdateTimer = nil
        // НЕ останавливаем continuousWorkTimer

        // 5. Обновляем UI
        updateStatusItem()
        updateMenu()

        logInfo("App", "✅ Частичный сброс после неактивности завершен")
    }

    // MARK: - Helper Methods for State Reset

    /// Закрывает все окна приложения
    private func closeAllWindows() {
        modernCompletionWindow?.orderOut(self)
        modernCompletionWindow = nil

        breakStartWindow?.orderOut(self)
        breakStartWindow = nil

        breakEndWindow?.orderOut(self)
        breakEndWindow = nil

        breakTypeWindow?.orderOut(self)
        breakTypeWindow = nil
    }

    /// Закрывает только окна уведомлений
    private func closeNotificationWindows() {
        // Закрываем только временные окна, оставляем основные
        modernCompletionWindow?.orderOut(self)
        modernCompletionWindow = nil
    }

    /// Показывает окно возвращения после отсутствия
    private func showReturnWindow(message: ActivityStateManager.ReturnMessage, duration: TimeInterval) {
        let minutes = Int(duration / 60)

        // Не показываем окна возвращения если таймер не активен (пользователь еще не начинал работать)
        guard pomodoroTimer.state == .working || pomodoroTimer.state == .overtime else {
            logInfo("App", "🎯 Таймер не активен (\(pomodoroTimer.state)) - не показываем окно возвращения")
            return
        }

        // Не показываем окно для коротких отсутствий
        guard message != .resumeSilently else {
            logInfo("App", "🎯 Короткое отсутствие (\(minutes) мин) - продолжаем молча")
            return
        }

        // Закрываем все предыдущие окна перед показом нового
        closeNotificationWindows()
        logInfo("App", "🎯 Закрыли предыдущие окна перед показом окна возвращения")

        // Создаем окно возвращения
        let returnWindow = BreakEndWindow()

        // Позиционируем окно относительно статус-бара
        if let statusItem = self.statusItem,
           let statusButton = statusItem.button,
           let statusWindow = statusButton.window {
            let statusFrame = statusWindow.convertToScreen(statusButton.frame)
            returnWindow.positionRelativeToStatusItem(statusItemFrame: statusFrame)
        }

        // Настраиваем режим в зависимости от типа сообщения
        switch message {
        case .resumeSilently:
            return // Уже обработано выше

        case .partialRest:
            returnWindow.configureForMode(.userReturnPartialRest(minutes: minutes))

        case .chooseRestOrWork:
            returnWindow.configureForMode(.userReturnChoiceRest(minutes: minutes))

        case .fullRest:
            returnWindow.configureForMode(.userReturnFullRest(minutes: minutes))
        }

        // Настраиваем обработчики
        returnWindow.onContinueRest = { [weak self] in
            logInfo("App", "🎯 Пользователь выбрал: Продолжить отдых")
            self?.breakEndWindow?.close()
            self?.breakEndWindow = nil

            // Запускаем официальный отдых с учетом уже отдохнутого времени
            self?.startOfficialRestWithTimeCredit(alreadyRestedMinutes: minutes)
        }

        returnWindow.onStartWork = { [weak self] in
            logInfo("App", "🎯 Пользователь выбрал: Начать работу")
            self?.breakEndWindow?.close()
            self?.breakEndWindow = nil
            // Здесь можно добавить логику начала работы если нужно
        }

        returnWindow.onStartIntervalWithProject = { [weak self] in
            logInfo("App", "🎯 Пользователь выбрал: Начать интервал с проектом")
            self?.breakEndWindow?.close()
            self?.breakEndWindow = nil
            self?.showProjectSelection()
        }

        // Сохраняем ссылку на окно для возможности закрытия при эскалации
        breakEndWindow = returnWindow

        // Показываем окно
        returnWindow.showWithAnimation()
        logInfo("App", "🎯 Показано окно возвращения для \(message) (\(minutes) мин)")
    }

    /// Запускает официальный отдых с учетом уже отдохнутого времени
    private func startOfficialRestWithTimeCredit(alreadyRestedMinutes: Int) {
        logInfo("App", "🎯 Запускаем официальный отдых с зачетом \(alreadyRestedMinutes) мин")

        // Определяем тип отдыха (короткий/длинный) на основе текущего состояния
        let isLongBreak = pomodoroTimer.shouldShowBreakTypeSelection()
        let standardBreakDuration = isLongBreak ? 15 : 5 // минуты

        // Вычисляем оставшееся время отдыха
        let remainingMinutes = max(1, standardBreakDuration - alreadyRestedMinutes)

        logInfo("App", "🎯 Стандартный отдых: \(standardBreakDuration) мин, уже отдохнул: \(alreadyRestedMinutes) мин, осталось: \(remainingMinutes) мин")

        // Запускаем отдых с скорректированным временем
        let remainingSeconds = TimeInterval(remainingMinutes * 60)
        pomodoroTimer.startBreakWithType(isLong: isLongBreak, testDuration: remainingSeconds)
    }

    /// Сбрасывает состояние переработки
    private func resetOvertimeState() {
        if pomodoroTimer.state == .overtime {
            logInfo("App", "🔄 Сбрасываем переработку - возвращаем в idle")
            pomodoroTimer.stopInterval()
        }
    }

    /// Останавливает все системы отслеживания
    private func stopAllTrackingSystems() {
        UnifiedReminderSystem.shared.stopEscalation()
        stopContinuousWorkTracking()
    }

    /// Сбрасывает флаги состояний
    private func resetStateFlags() {
        userPromisedToRest = false
        userRestingAtComputer = false
        isBreakPostponed = false
    }

    /// Останавливает все таймеры
    private func stopAllTimers() {
        postponeTimer?.invalidate()
        postponeTimer = nil

        postponeUpdateTimer?.invalidate()
        postponeUpdateTimer = nil

        continuousWorkTimer?.invalidate()
        continuousWorkTimer = nil
    }

    /// Сбрасывает все состояния после длительного сна (более 15 минут) - DEPRECATED
    private func resetAllStatesAfterLongSleep(sleepMinutes: Int) {
        // Перенаправляем на новый метод для обратной совместимости
        resetAllStatesAfterSleep(sleepMinutes: sleepMinutes, wasRealSleep: true)
    }

    /// Показывает уведомление о сбросе состояний после сна/неактивности
    private func showSleepResetNotification(sleepMinutes: Int, wasRealSleep: Bool) {
        let eventType = wasRealSleep ? "сна" : "неактивности"
        let title = "Состояния сброшены"
        let message = "После \(sleepMinutes) минут \(eventType) состояния приложения были сброшены"

        showSleepNotification(title, message)
    }

    /// Показывает уведомление о сбросе состояний после длительного сна - DEPRECATED
    private func showLongSleepResetNotification(sleepMinutes: Int) {
        // Показываем ненавязчивое уведомление через логи
        logInfo("App", "💡 Состояния сброшены после \(sleepMinutes) минут сна")

        // Можно добавить системное уведомление, если нужно:
        // let notification = NSUserNotification()
        // notification.title = "uProd"
        // notification.informativeText = "Состояния сброшены после длительного отсутствия (\(sleepMinutes) мин)"
        // NSUserNotificationCenter.default.deliver(notification)
    }
}

// MARK: - SimpleUnifiedSystemDelegate
extension AppDelegate {
    /// Показать напоминание определенного уровня
    func showEscalationReminder(minutes: Int, level: Int, for intervalType: String) {
        print("🔔 Показываем напоминание: \(minutes) минут, уровень \(level), тип \(intervalType)")

        // Закрываем окна возвращения при показе эскалации
        if let returnWindow = breakEndWindow {
            logInfo("App", "🎯 Закрываем окно возвращения при показе эскалации")
            returnWindow.orderOut(self)
            breakEndWindow = nil
        }

        // 🚨 КРИТИЧЕСКАЯ ЗОНА: Специальное поведение для уровня 4
        if level >= 4 {
            print("🚨 КРИТИЧЕСКАЯ ЗОНА: Показываем блокирующее полноэкранное окно")
            showCriticalZoneWindow(minutes: minutes, intervalType: intervalType)
            return
        }

        // Показываем или обновляем окно
        if let window = modernCompletionWindow {
            // Окно уже существует - обновляем его С ТРЯСКОЙ (как в формальных интервалах)
            print("🔄 Обновляем существующее окно с тряской для уровня \(level)")
            window.updateMessage(reminderCount: level)
        } else {
            // Создаем новое окно
            print("🆕 Создаем новое окно для уровня \(level)")
            if intervalType == "informal" {
                // Для неформальных интервалов:
                // - Уровень 0: показываем "Time for rest" (первое неформальное окно)
                // - Уровень 1+: показываем эскалацию переработки
                if level == 0 {
                    showUnifiedReminder(type: .informalWorkDetected)
                } else {
                    showUnifiedReminder(type: .continuousWorkReminder(minutes: minutes, level: level))
                }
            } else {
                showUnifiedReminder(type: .formalIntervalCompleted)
                // Обновляем для правильного уровня
                modernCompletionWindow?.updateMessage(reminderCount: level)
            }
        }

        print("✅ Напоминание показано")
    }

    /// Показать критическое полноэкранное окно для уровня 4+ с предварительным обратным отсчетом
    private func showCriticalZoneWindow(minutes: Int, intervalType: String) {
        print("🚨 Показываем обратный отсчет перед критическим окном")

        // Закрываем обычное окно если оно есть
        if let window = modernCompletionWindow {
            window.orderOut(self)
            modernCompletionWindow = nil
        }

        // Показываем обратный отсчет на 5 секунд
        let countdown = CountdownWindow(
            duration: 5,
            completion: { [weak self] in
                // После завершения отсчета показываем критическое окно
                self?.showActualCriticalZoneWindow(minutes: minutes, intervalType: intervalType)
            },
            onCancel: { [weak self] in
                // При отмене (ESC) даем отсрочку на 2 минуты
                print("⏰ Пользователь отменил критическое окно - отсрочка на 2 минуты")
                self?.postponeCriticalZone(minutes: minutes, intervalType: intervalType)
            }
        )

        self.countdownWindow = countdown
        countdown.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        print("⏰ Обратный отсчет запущен")
    }

    /// Показать само критическое окно (после обратного отсчета)
    private func showActualCriticalZoneWindow(minutes: Int, intervalType: String) {
        print("🚨 Создаем критическое полноэкранное окно")

        // Убираем окно обратного отсчета
        countdownWindow = nil

        // Создаем полноэкранное критическое окно
        let criticalWindow = CriticalZoneWindow(minutes: minutes, intervalType: intervalType)
        criticalWindow.criticalDelegate = self

        // Сохраняем ссылку на критическое окно
        self.criticalZoneWindow = criticalWindow

        // Показываем с анимацией
        criticalWindow.showWithAnimation()

        print("🚨 Критическое окно показано")
    }

    /// Отсрочка критического окна на 2 минуты
    private func postponeCriticalZone(minutes: Int, intervalType: String) {
        // Убираем окно обратного отсчета
        countdownWindow = nil

        print("⏰ Отсрочка критического окна на 2 минуты")

        // Через 2 минуты снова показываем обратный отсчет
        DispatchQueue.main.asyncAfter(deadline: .now() + 120.0) { [weak self] in
            print("⏰ Отсрочка закончилась - снова показываем критическое окно")
            self?.showCriticalZoneWindow(minutes: minutes, intervalType: intervalType)
        }
    }

    /// Обновить статус-бар с информацией о переработке (с секундами)
    func updateStatusBar(overtimeMinutes: Int, totalMinutes: Int, seconds: Int) {
        print("📊 Обновляем статус-бар: общее время \(totalMinutes):\(String(format: "%02d", seconds)), переработка \(overtimeMinutes) мин")

        // Используем существующую проверенную логику статус-бара (как в getContinuousWorkTextColor)
        let textColor: NSColor
        let emoji: String

        if overtimeMinutes > 0 {
            emoji = "⚠️"
            // Используем время ПЕРЕРАБОТКИ для правильных цветов
            switch overtimeMinutes {
            case 1...2:
                textColor = NSColor.systemYellow
                print("🟡 Желтая зона: \(overtimeMinutes) мин переработки")
            case 3...4:
                textColor = NSColor.systemOrange
                print("🟠 Оранжевая зона: \(overtimeMinutes) мин переработки")
            default:
                textColor = NSColor.systemRed
                print("🔴 Красная зона: \(overtimeMinutes) мин переработки")
            }
        } else {
            emoji = "⏱️"
            textColor = NSColor.controlTextColor
        }

        // Формат показывает ОБЩЕЕ время: ⏱️ 53:23 или ⚠️ 53:23
        let statusText = String(format: "%@ %d:%02d", emoji, totalMinutes, seconds)

        // Создаем attributed string с цветом (как в updateStatusItemForContinuousWork)
        DispatchQueue.main.async { [weak self] in
            let attributedString = NSMutableAttributedString()
            let font = NSFont.monospacedSystemFont(ofSize: 13, weight: .medium)

            for char in statusText {
                let charAttributed = NSAttributedString(string: String(char), attributes: [
                    .font: font,
                    .foregroundColor: textColor
                ])
                attributedString.append(charAttributed)
            }

            // Обновляем статус бар (как в updateStatusItemForContinuousWork)
            self?.statusItem.button?.image = nil
            self?.statusItem.button?.title = ""
            self?.statusItem.button?.attributedTitle = attributedString

            // Принудительно перерисовываем
            if let button = self?.statusItem.button {
                button.setNeedsDisplay(button.bounds)
            }
        }

        print("✅ Статус-бар обновлен: +\(totalMinutes):\(String(format: "%02d", seconds))")
    }

    /// Записывает статистику интервала через StatisticsManager
    func recordIntervalStatistics(duration: TimeInterval, intervalType: String) {
        statisticsManager.recordCompletedInterval(duration: duration, intervalType: intervalType)
        print("📊 AppDelegate: Записана статистика через делегат - \(Int(duration/60)) мин (\(intervalType))")
    }

}

// MARK: - CriticalZoneWindowDelegate
extension AppDelegate {
    func criticalZoneWindowDidTakeBreak() {
        print("🌿 AppDelegate: Пользователь взял отдых из критического окна")

        // Закрываем критическое окно
        criticalZoneWindow?.orderOut(self)
        criticalZoneWindow = nil

        // Останавливаем систему эскалации
        SimpleUnifiedSystem.shared.stopEscalation()

        // Запускаем отдых через унифицированную систему
        startUnifiedBreak(isFromFormalInterval: false)

        print("✅ Отдых начат из критического окна")
    }
}
