import Foundation

/// Детектор неформальных рабочих сессий
/// Отслеживает длительную работу без запущенного Pomodoro-интервала
/// и предлагает пользователю сделать перерыв
class InformalSessionDetector {
    
    // MARK: - Properties
    
    /// История активности по минутам (true = активная минута, false = неактивная)
    private var minuteActivityLog: [Bool] = []
    
    /// Максимальный размер лога (храним больше чем анализируем для запаса)
    private let maxLogSize = 60
    
    /// Размер окна для анализа (последние N минут)
    private let checkWindowSize = 52

    /// Минимальное количество активных минут для срабатывания
    private let minActiveMinutes = 42 // 80% из 52 минут
    
    /// Время последнего показа предложения отдыха
    var lastSuggestionTime: Date?
    
    /// Период "охлаждения" - минимальное время между предложениями отдыха
    private let cooldownPeriod: TimeInterval = 10 * 60 // 10 минут (было 30)
    
    /// Включен ли детектор
    private var isEnabled = true
    
    // MARK: - Dependencies

    weak var pomodoroTimer: PomodoroTimer?
    private weak var breakTimer: BreakTimer?

    /// Новая система отслеживания активности с 4-бандовым обнаружением
    private let minuteActivityTracker: MinuteActivityTracker

    /// Таймер для автоматической проверки активности каждую минуту
    private var activityCheckTimer: Timer?
    
    // MARK: - Callbacks
    
    /// Вызывается когда нужно показать предложение отдыха
    var onRestSuggestionNeeded: (() -> Void)?
    
    // MARK: - Initialization
    
    init(pomodoroTimer: PomodoroTimer, breakTimer: BreakTimer) {
        self.pomodoroTimer = pomodoroTimer
        self.breakTimer = breakTimer
        self.minuteActivityTracker = MinuteActivityTracker()

        print("🔍 InformalSessionDetector: Инициализирован с новой системой активности")
        setupActivityTracking()
    }

    /// Инициализатор для тестов
    init() {
        self.pomodoroTimer = nil
        self.breakTimer = nil
        self.minuteActivityTracker = MinuteActivityTracker()

        print("🔍 InformalSessionDetector: Инициализирован для тестов с новой системой активности")
        setupActivityTracking()
    }

    /// Инициализатор для тестов с кастомным MinuteActivityTracker
    init(minuteActivityTracker: MinuteActivityTracker) {
        self.pomodoroTimer = nil
        self.breakTimer = nil
        self.minuteActivityTracker = minuteActivityTracker

        print("🔍 InformalSessionDetector: Инициализирован для тестов с кастомным трекером")
        setupActivityTracking()
    }
    
    // MARK: - Public Methods

    /// Запускает автоматическое отслеживание активности
    func startTracking() {
        guard isEnabled else { return }

        // Запускаем MinuteActivityTracker
        minuteActivityTracker.startTracking()

        // Запускаем таймер для проверки активности каждую минуту
        activityCheckTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
            self?.checkAndRecordMinuteActivity()
        }

        print("🔍 InformalSessionDetector: Автоматическое отслеживание запущено")
    }

    /// Останавливает автоматическое отслеживание активности
    func stopTracking() {
        activityCheckTimer?.invalidate()
        activityCheckTimer = nil

        print("🔍 InformalSessionDetector: Автоматическое отслеживание остановлено")
    }

    /// Проверяет и записывает активность за текущую минуту
    private func checkAndRecordMinuteActivity() {
        guard isEnabled else { return }

        // КРИТИЧЕСКИ ВАЖНО: Не записываем активность если формальный таймер активен!
        if let timer = pomodoroTimer, timer.state != .idle {
            logInfo("InformalDetector", "🔍 checkAndRecordMinuteActivity: Формальный таймер активен (\(timer.state)), пропускаем проверку активности")
            return
        }

        // Принудительно завершаем текущую минуту и получаем результат
        minuteActivityTracker.forceCompleteCurrentMinute()
        let wasActive = minuteActivityTracker.wasCurrentMinuteActive()

        // Записываем активность через существующий метод
        recordMinuteActivity(isActive: wasActive)

        logInfo("InformalDetector", "🔍 checkAndRecordMinuteActivity: Автоматическая проверка - активность \(wasActive ? "ДА" : "НЕТ")")
    }

    /// Записывает активность за минуту
    /// - Parameter isActive: была ли активность в эту минуту
    func recordMinuteActivity(isActive: Bool) {
        guard isEnabled else { return }

        // КРИТИЧЕСКИ ВАЖНО: Не записываем активность если формальный таймер активен!
        if let timer = pomodoroTimer, timer.state != .idle {
            logInfo("InformalDetector", "🔍 recordMinuteActivity: Формальный таймер активен (\(timer.state)), пропускаем запись активности")
            return
        }

        // Записываем активность только если пользователь действительно активен
        // Это предотвращает засчитывание "активных" минут когда пользователь отошел
        if !isActive {
            logInfo("InformalDetector", "🔍 recordMinuteActivity: Пользователь неактивен, записываем неактивную минуту")
        }

        logInfo("InformalDetector", "🔍 recordMinuteActivity: Записываем активность \(isActive ? "ДА" : "НЕТ")")
        minuteActivityLog.append(isActive)

        // Ограничиваем размер лога
        if minuteActivityLog.count > maxLogSize {
            minuteActivityLog.removeFirst()
        }

        // Проверяем на длительные перерывы в активности
        checkForLongInactivityPeriod()

        let activeCount = minuteActivityLog.filter { $0 }.count
        print("🔍 InformalSessionDetector: Записана активность \(isActive ? "ДА" : "НЕТ"). Лог: \(minuteActivityLog.count) мин, активных: \(activeCount)")

        // Проверяем только если накопилось достаточно данных
        if minuteActivityLog.count >= checkWindowSize {
            checkForRestSuggestion(isTestMode: false)
        }
    }
    
    /// Сбрасывает историю активности (например, при начале формального интервала)
    func resetActivityHistory() {
        minuteActivityLog.removeAll()
        print("🔍 InformalSessionDetector: История активности сброшена")
    }

    // MARK: - Private Methods

    /// Настраивает автоматическое отслеживание активности
    private func setupActivityTracking() {
        // Автоматически запускаем отслеживание при инициализации
        // В реальном приложении это будет вызываться из AppDelegate
        startTracking()
    }
    
    /// Включает/выключает детектор
    func setEnabled(_ enabled: Bool) {
        isEnabled = enabled
        if !enabled {
            resetActivityHistory()
        }
        print("🔍 InformalSessionDetector: \(enabled ? "Включен" : "Выключен")")
    }
    
    /// Сбрасывает cooldown (для тестирования)
    func resetCooldown() {
        lastSuggestionTime = nil
        print("🔍 InformalSessionDetector: Cooldown сброшен")
    }

    /// Заполняет историю активными минутами для тестирования
    func fillWithActiveMinutesForTesting() {
        logInfo("InformalDetector", "🔍 fillWithActiveMinutesForTesting() вызван")

        // В режиме тестирования ВСЕГДА разрешаем тест
        logInfo("InformalDetector", "🔍 Режим тестирования - пропускаем проверку isAppIdle()")

        minuteActivityLog.removeAll()
        // Заполняем 52 активными минутами
        for _ in 0..<checkWindowSize {
            minuteActivityLog.append(true)
        }
        logInfo("InformalDetector", "🔍 Заполнено \(checkWindowSize) активными минутами для тестирования")

        // Сразу проверяем условия в тестовом режиме
        checkForRestSuggestion(isTestMode: true)
    }
    
    // MARK: - Private Methods
    
    /// Проверяет условия для показа предложения отдыха
    private func checkForRestSuggestion(isTestMode: Bool = false) {
        logInfo("InformalDetector", "🔍 checkForRestSuggestion() вызван (тест: \(isTestMode))")

        // 0. КРИТИЧЕСКИ ВАЖНО: Проверяем минимум данных
        guard minuteActivityLog.count >= checkWindowSize else {
            logInfo("InformalDetector", "🔍 ❌ Недостаточно данных (\(minuteActivityLog.count) < \(checkWindowSize)), пропускаем")
            return
        }

        // 1. Проверяем, что формальный таймер не активен (пропускаем в тестовом режиме)
        if !isTestMode {
            guard isPomodoroTimerIdle() else {
                logInfo("InformalDetector", "🔍 Формальный таймер активен, пропускаем проверку")
                return
            }
        } else {
            logInfo("InformalDetector", "🔍 Тестовый режим - пропускаем проверку состояния таймера")
        }

        // 2. Проверяем cooldown (пропускаем в тестовом режиме)
        if !isTestMode {
            guard canShowSuggestion() else {
                logInfo("InformalDetector", "🔍 Cooldown еще не истек, пропускаем")
                return
            }
        } else {
            logInfo("InformalDetector", "🔍 Тестовый режим - пропускаем проверку cooldown")
        }

        // 3. Анализируем активность за последние N минут
        let recentActivity = Array(minuteActivityLog.suffix(checkWindowSize))
        let activeCount = recentActivity.filter { $0 }.count

        logInfo("InformalDetector", "🔍 Анализ последних \(checkWindowSize) минут: \(activeCount) активных из \(recentActivity.count)")

        if activeCount >= minActiveMinutes {
            logInfo("InformalDetector", "🔍 ✅ Условия выполнены! Показываем предложение отдыха")
            showRestSuggestion()
        } else {
            logInfo("InformalDetector", "🔍 Недостаточно активности (\(activeCount) < \(minActiveMinutes))")
        }
    }
    
    /// Проверяет, можно ли показать предложение отдыха (только состояние таймера)
    func isPomodoroTimerIdle() -> Bool {
        // Проверяем состояние Pomodoro таймера
        if let timer = pomodoroTimer {
            logInfo("InformalDetector", "🔍 Состояние Pomodoro таймера: \(timer.state)")
            guard timer.state == .idle else {
                logInfo("InformalDetector", "🔍 Pomodoro таймер НЕ в состоянии idle, пропускаем")
                return false
            }
            return true
        } else {
            logInfo("InformalDetector", "🔍 ⚠️ Pomodoro таймер недоступен!")
            return false
        }
    }

    /// УСТАРЕВШИЙ метод - оставлен для совместимости
    /// Используйте isPomodoroTimerIdle() вместо этого
    func isAppIdle() -> Bool {
        return isPomodoroTimerIdle()
    }
    
    /// Проверяет, можно ли показать предложение (cooldown)
    private func canShowSuggestion() -> Bool {
        guard let lastTime = lastSuggestionTime else {
            return true // Первый раз
        }
        
        let timeSinceLastSuggestion = Date().timeIntervalSince(lastTime)
        return timeSinceLastSuggestion >= cooldownPeriod
    }
    
    /// Показывает предложение отдыха
    private func showRestSuggestion() {
        logInfo("InformalDetector", "🔍 🎯 Показываем предложение отдыха!")
        lastSuggestionTime = Date()
        onRestSuggestionNeeded?()
        logInfo("InformalDetector", "🔍 ✅ Предложение отдыха показано")
    }

    /// Проверяет на длительные периоды неактивности и сбрасывает лог если нужно
    private func checkForLongInactivityPeriod() {
        // Проверяем только если есть достаточно данных
        guard minuteActivityLog.count >= 15 else { return }

        // Смотрим на последние 15 минут
        let recentActivity = Array(minuteActivityLog.suffix(15))
        let activeInRecent = recentActivity.filter { $0 }.count

        // Если в последние 15 минут было меньше 2 активных минут - считаем это длительным перерывом
        if activeInRecent <= 1 {
            print("🔍 InformalSessionDetector: 🌙 Обнаружен длительный перерыв (\(activeInRecent) активных из 15 минут) - сбрасываем лог")
            resetActivityHistory()
        }
    }
    
    // MARK: - Debug Methods

    /// Возвращает текущую статистику для отладки
    func getDebugInfo() -> String {
        let recentActivity = Array(minuteActivityLog.suffix(checkWindowSize))
        let activeCount = recentActivity.filter { $0 }.count
        let totalMinutes = minuteActivityLog.count

        // ПОЛНАЯ проверка всех условий
        let hasEnoughData = totalMinutes >= checkWindowSize
        let hasEnoughActivity = activeCount >= minActiveMinutes
        let timerIdle = isPomodoroTimerIdle()
        let cooldownOk = canShowSuggestion()
        let wouldTrigger = shouldTriggerRestSuggestion()

        var info = "InformalSessionDetector Debug:\n"
        info += "- Всего минут в логе: \(totalMinutes)\n"
        info += "- Активных в последних \(checkWindowSize): \(activeCount)/\(recentActivity.count)\n"
        info += "- Требуется для срабатывания: \(minActiveMinutes)\n"
        info += "\n"
        info += "🔍 СОСТОЯНИЕ СИСТЕМЫ:\n"
        info += "- Достаточно данных (≥\(checkWindowSize)): \(hasEnoughData ? "ДА" : "НЕТ")\n"
        info += "- Достаточно активности (≥\(minActiveMinutes)): \(hasEnoughActivity ? "ДА" : "НЕТ")\n"
        info += "- Формальный таймер в покое: \(timerIdle ? "ДА" : "НЕТ")\n"
        // Детальная информация о cooldown
        if let lastTime = lastSuggestionTime {
            let timeSinceLastSuggestion = Date().timeIntervalSince(lastTime)
            let timeUntilNext = cooldownPeriod - timeSinceLastSuggestion
            if timeUntilNext > 0 {
                let minutesUntilNext = Int(timeUntilNext / 60)
                info += "- Cooldown истек: НЕТ (осталось \(minutesUntilNext) мин)\n"
            } else {
                info += "- Cooldown истек: ДА\n"
            }
        } else {
            info += "- Cooldown истек: ДА (первый раз)\n"
        }
        info += "\n"
        info += "🎯 РЕЗУЛЬТАТ: \(wouldTrigger ? "СРАБОТАЕТ" : "НЕ СРАБОТАЕТ")\n"
        info += "- Включен: \(isEnabled)"

        return info
    }

    /// Заполняет лог тестовыми данными для симуляции проблемного сценария
    /// 30 минут активности + перерыв + 20 минут активности
    func fillWithTestScenario() {
        minuteActivityLog.removeAll()

        // 30 минут активности
        for _ in 0..<30 {
            minuteActivityLog.append(true)
        }

        // 2 минуты неактивности (симуляция перерыва)
        for _ in 0..<2 {
            minuteActivityLog.append(false)
        }

        // 20 минут активности
        for _ in 0..<20 {
            minuteActivityLog.append(true)
        }

        print("🔍 InformalSessionDetector: 🧪 Заполнен тестовый сценарий: 30+2+20 минут")
        print("🔍 InformalSessionDetector: Активных минут: \(minuteActivityLog.filter { $0 }.count)/\(minuteActivityLog.count)")

        // Проверяем условия в тестовом режиме
        if minuteActivityLog.count >= checkWindowSize {
            checkForRestSuggestion(isTestMode: true)
        }
    }

    /// Принудительно показывает предложение отдыха (для тестирования)
    func forceShowRestSuggestion() {
        print("🔍 InformalSessionDetector: 🧪 Принудительный показ предложения отдыха")
        logInfo("InformalDetector", "🧪 Принудительный показ предложения отдыха")

        // Сбрасываем время последнего предложения чтобы окно точно показалось
        lastSuggestionTime = nil

        showRestSuggestion()
    }

    /// Проверяет, должно ли сработать предложение отдыха (для автотестов)
    func shouldTriggerRestSuggestion() -> Bool {
        // Проверяем, что накопилось достаточно данных
        guard minuteActivityLog.count >= checkWindowSize else {
            return false
        }

        // Анализируем активность за последние N минут
        let recentActivity = Array(minuteActivityLog.suffix(checkWindowSize))
        let activeCount = recentActivity.filter { $0 }.count

        // Проверяем условие срабатывания
        return activeCount >= minActiveMinutes
    }

    /// Возвращает детальную информацию о логе активности
    func getActivityLogDetails() -> String {
        let recentActivity = Array(minuteActivityLog.suffix(checkWindowSize))
        let activeCount = recentActivity.filter { $0 }.count

        var details = "Лог активности (последние \(min(checkWindowSize, minuteActivityLog.count)) минут):\n"

        // Показываем последние 20 записей для краткости
        let displayCount = min(20, recentActivity.count)
        let displayActivity = Array(recentActivity.suffix(displayCount))

        for (index, isActive) in displayActivity.enumerated() {
            let symbol = isActive ? "🟢" : "⚫"
            details += "\(symbol)"
            if (index + 1) % 10 == 0 {
                details += "\n"
            }
        }

        details += "\n\nИтого: \(activeCount) активных из \(recentActivity.count) минут"
        details += "\nТребуется: \(minActiveMinutes) для срабатывания"

        return details
    }

    // MARK: - Cleanup

    deinit {
        stopTracking()
        print("🔍 InformalSessionDetector: Деинициализирован")
    }
}
