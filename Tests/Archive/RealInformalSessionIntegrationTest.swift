import Foundation

/// ТЕСТ РЕАЛЬНОЙ ИНТЕГРАЦИИ InformalSessionDetector с MinuteActivityTracker
/// Проверяем что интеграция работает в реальных условиях

@main
struct RealInformalSessionIntegrationTest {
    static func main() {
        print("🚀 ТЕСТ РЕАЛЬНОЙ ИНТЕГРАЦИИ InformalSessionDetector")
        print(String(repeating: "=", count: 60))
        
        // Создаем РЕАЛЬНЫЙ объект
        let detector = InformalSessionDetector()
        
        var passed = 0
        var total = 0
        
        // СЦЕНАРИЙ 1: Включение детектора
        total += 1
        print("\n📋 СЦЕНАРИЙ 1: Включение детектора")
        
        detector.setEnabled(true)
        
        let debugAfterEnable = detector.getDebugInfo()
        if debugAfterEnable.contains("Включен: true") {
            print("✅ ПРОЙДЕН: Детектор корректно включается")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Детектор НЕ включается")
            print("   Debug: \(debugAfterEnable)")
        }
        
        // СЦЕНАРИЙ 2: Запуск автоматического отслеживания
        total += 1
        print("\n📋 СЦЕНАРИЙ 2: Запуск автоматического отслеживания")
        
        detector.startTracking()
        
        // Проверяем что система запустилась (через debug info)
        let debugInfo = detector.getDebugInfo()

        if debugInfo.contains("Всего минут: 0") {
            print("✅ ПРОЙДЕН: Автоматическое отслеживание запускается")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Неожиданное начальное состояние")
            print("   Debug: \(debugInfo)")
        }
        
        // СЦЕНАРИЙ 3: Симуляция активности и проверка записи
        total += 1
        print("\n📋 СЦЕНАРИЙ 3: Симуляция активности и проверка записи")

        // Записываем активность напрямую
        detector.recordMinuteActivity(isActive: true)

        // Ждем немного для обработки
        Thread.sleep(forTimeInterval: 0.1)

        let debugAfterActivity = detector.getDebugInfo()

        if debugAfterActivity.contains("Всего минут: 1") {
            print("✅ ПРОЙДЕН: Активность корректно записывается")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Активность НЕ записывается")
            print("   Debug после активности: \(debugAfterActivity)")
        }
        
        // СЦЕНАРИЙ 4: Проверка интеграции с новой системой
        total += 1
        print("\n📋 СЦЕНАРИЙ 4: Проверка интеграции с новой системой")

        // Проверяем что debug info содержит информацию о новой системе
        let debugForNewSystem = detector.getDebugInfo()

        if debugForNewSystem.contains("MinuteActivityTracker") || debugForNewSystem.contains("4-бандов") {
            print("✅ ПРОЙДЕН: Интеграция с новой системой работает")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: НЕ видно интеграции с новой системой")
            print("   Debug: \(debugForNewSystem)")
        }
        
        // СЦЕНАРИЙ 5: Обратная совместимость - старые методы работают
        total += 1
        print("\n📋 СЦЕНАРИЙ 5: Обратная совместимость - старые методы работают")

        // Проверяем что старые методы все еще работают
        let canShowSuggestion = detector.shouldTriggerRestSuggestion()

        // Этот метод должен работать без ошибок (результат может быть любым)
        print("✅ ПРОЙДЕН: Старые методы работают без ошибок")
        print("   shouldTriggerRestSuggestion: \(canShowSuggestion)")
        passed += 1
        
        // СЦЕНАРИЙ 6: Остановка отслеживания
        total += 1
        print("\n📋 СЦЕНАРИЙ 6: Остановка отслеживания")
        
        detector.stopTracking()
        
        // Проверяем что система остановилась (детектор должен остаться включенным)
        let debugAfterStop = detector.getDebugInfo()
        if debugAfterStop.contains("Включен: true") {
            print("✅ ПРОЙДЕН: Отслеживание останавливается, детектор остается включенным")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Детектор отключился при остановке отслеживания")
            print("   Debug: \(debugAfterStop)")
        }
        
        // СЦЕНАРИЙ 7: Отключение детектора
        total += 1
        print("\n📋 СЦЕНАРИЙ 7: Отключение детектора")
        
        detector.setEnabled(false)
        
        let debugAfterDisable = detector.getDebugInfo()
        if debugAfterDisable.contains("Включен: false") {
            print("✅ ПРОЙДЕН: Детектор корректно отключается")
            passed += 1
        } else {
            print("❌ ПРОВАЛЕН: Детектор НЕ отключается")
            print("   Debug: \(debugAfterDisable)")
        }
        
        // ИТОГИ
        print("\n" + String(repeating: "=", count: 60))
        print("📊 РЕЗУЛЬТАТЫ: \(passed)/\(total) сценариев пройдено")
        
        if passed == total {
            print("🎉 ВСЕ СЦЕНАРИИ ПРОЙДЕНЫ!")
            print("✅ Интеграция InformalSessionDetector работает корректно")
            
            // Проверка обнаружения поломок
            print("\n🔨 ПРОВЕРКА ОБНАРУЖЕНИЯ ПОЛОМОК:")
            testBreakageDetection()
        } else {
            print("⚠️  ЕСТЬ ПРОБЛЕМЫ В ИНТЕГРАЦИИ!")
            exit(1)
        }
    }
    
    static func testBreakageDetection() {
        print("🔨 Инструкция для проверки обнаружения поломок:")
        print("1. Откройте SimplePomodoroTest/InformalSessionDetector.swift")
        print("2. В методе checkAndRecordMinuteActivity() найдите строку:")
        print("   if wasActive {")
        print("3. Замените на:")
        print("   if false {")
        print("4. Запустите этот тест снова")
        print("5. СЦЕНАРИЙ 3 должен провалиться!")
        print("6. Верните код обратно")
        print("\nЭто покажет что тест ловит поломки в реальной интеграции.")
        
        exit(0)
    }
}
