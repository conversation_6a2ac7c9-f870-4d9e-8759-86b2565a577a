# 🌅 ЗАДАЧА: СИСТЕМА РАННЕГО ВОВЛЕЧЕНИЯ

## 🎯 ГЛАВНАЯ ЦЕЛЬ
Помочь пользователю **регулярно начинать работу** над приоритетным проектом утром с помощью **двойной системы эскалации**, принципа **"план минимум"** и **адаптивных интервалов**.

## 🔄 КЛЮЧЕВЫЕ ПРИНЦИПЫ

### **Принцип "Интервал начинается сразу"**
Как только пользователь отвечает на любой вопрос → интервал уже начался, продолжаем направлять его в работу.

### **Двойная система эскалации:**
1. **По дням без работы** (долгосрочная) - влияет на тип первого сообщения
2. **По времени в течение дня** (краткосрочная) - повторные предложения с дескалацией интервалов

### **Дескалация интервалов в течение дня:**
- Первое предложение: полный интервал (например, 52 минуты)
- Второе предложение: сокращенный интервал (например, 25 минут)  
- Третье предложение: план минимум (2-3 минуты)
- Финальное: полноэкранное "сейчас не начнешь - сегодня не начнешь"

### **Адаптивные интервалы по дням:**
- Работал вчера → стандартный интервал (52 мин)
- Не работал 1 день → сокращенный интервал (30 мин)
- Не работал 2+ дня → короткий интервал (15 мин)
- План минимум всегда доступен (2-3 мин)

---

## 📋 ЭТАПЫ РЕАЛИЗАЦИИ

### **ЭТАП 1: Детектор пробуждения** 🌅
**Что делаем:** Система понимает, когда пользователь проснулся и включил компьютер

**Результат:** 
- Предложение начать работу в самый продуктивный момент
- Стартовые сообщения сразу после пробуждения

**Технические детали:**
- Отслеживание времени последней активности
- Определение "первого запуска дня"
- Интеграция с системой уведомлений

---

### **ЭТАП 2: Умная аналитика дней без работы** 📊
**Что делаем:** Отслеживаем статистику работы над приоритетным проектом

**Результат:**
- Система "помнит" историю работы
- Основа для адаптивных сообщений и интервалов
- Статистика для эскалации

**Категории:**
- **0 дней** (работал вчера) → стандартные интервалы
- **1-2 дня** → сокращенные интервалы + мягкая мотивация
- **3-5 дней** → короткие интервалы + усиленная мотивация  
- **Неделя+** → план минимум + критическая эскалация

---

### **ЭТАП 3: Система умных сообщений с двойной эскалацией** 💬🚨

#### **3.1 Первые сообщения (зависят от дней без работы):**

**0 дней (работал вчера):**
```
"Доброе утро! Готовы продолжить работу над [Проект]?
Предлагаю стандартный интервал 52 минуты.

[Начать 52 мин] [Другой интервал] [Не сейчас]"
```

**1-2 дня:**
```
"Привет! Давайте вернемся к [Проект].
Предлагаю сокращенный интервал 30 минут.

[Начать 30 мин] [План минимум 3 мин] [Не сейчас]"
```

**3-5 дней:**
```
"Скучаю по работе над [Проект]! 
Начнем с короткого интервала 15 минут?

[Начать 15 мин] [План минимум 3 мин] [Не сейчас]"
```

**Неделя+ (ПОЛНЫЙ ЭКРАН):**
```
"🚨 СТОП! Неделя без работы над [Проект]

Этот проект все еще важен?

[Да, начать 15 мин] [План минимум 3 мин] [Закрыть проект] [Не сейчас]"
```

#### **3.2 Повторные сообщения в течение дня (дескалация):**

**Второе предложение:**
```
"Может, попробуем сокращенный интервал?
Лучше 25 минут сейчас, чем 2 часа потом.

[Начать 25 мин] [План минимум] [Не сегодня]"
```

**Третье предложение:**
```
"Хорошо, давайте план минимум - всего 3 минуты.
Streak не прервется, регулярность важнее интенсивности.

[План минимум 3 мин] [Не сегодня]"
```

**Финальное (ПОЛНЫЙ ЭКРАН):**
```
"⏰ ПОСЛЕДНИЙ ШАНС

Если не начнешь СЕЙЧАС, то не начнешь СЕГОДНЯ.

Всего 2 минуты - и streak сохранен.

[2 минуты] [Честно: не хочу сегодня]"
```

#### **3.3 Проверка готовности (встроена в сообщения):**

**Вариант с проверкой ясности:**
```
"Готовы поработать над [Проект]?

Но сначала: ВАМ ЯСНО ЧТО ДЕЛАТЬ?

[Да, ясно - начать] [Не очень - проясню] [Совсем не ясно]"
```

**Ответы:**
- **"Да, ясно"** → Начинаем предложенный интервал
- **"Не очень"** → "Проясни задачи - это тоже считается интервалом!" → Начинаем интервал планирования
- **"Совсем не ясно"** → "10 минут на прояснение - и это уже работа!" → Интервал прояснения

---

### **ЭТАП 4: Система "План минимум"** ⭐
**Что делаем:** Внедряем принцип минимальной планки каждый день

**Ключевые принципы:**
- **"Лучше 2 минуты, чем ничего"**
- **"Одно отжимание лучше нуля"** 
- **Сохранение streak'а и регулярности**
- **Честный выбор: план минимум или "не хочу сегодня"**

**Варианты плана минимум:**
- 2-3 минуты работы
- Прочитать одну страницу
- Написать одно предложение  
- Открыть файл проекта и посмотреть

**Рост плана минимум:**
- Начинаем с 2 минут
- Постепенно растим до 5 минут (отдельная задача)

---

### **ЭТАП 5: Адаптивные интервалы** ⚙️
**Что делаем:** Автоматически подстраиваем длительность интервалов под историю работы

**Логика адаптации:**
- **Работал вчера** → стандартный интервал (52 мин)
- **Не работал 1 день** → сокращенный (30 мин)  
- **Не работал 2+ дня** → короткий (15 мин)
- **План минимум** → всегда доступен (2-3 мин)

**Комментарий:** Пока делаем хардкод значений, но подразумеваем автоматический расчет в будущем.

---

### **ЭТАП 6: Заглушки для системы барьеров** 🔧
**Что делаем:** Простые текстовые ответы на барьеры, пометки для будущего

**Заглушки:**
- **Неясность задач** → "Проясни - это тоже интервал!" → Интервал планирования
- **Лень** → "План минимум спасет streak" → Предложение 2-3 минут
- **Нет времени** → "2 минуты всегда найдутся" → План минимум
- **Проект не важен** → "Тогда закрой честно" → Опция закрытия проекта

**🔮 Пометки для будущего (Задача 4 - Барьеры):**
- Многоуровневая интерактивная система (4-10 уровней вопросов)
- Глубокая проработка каждого барьера
- Система помощи с планированием и прояснением
- Анализ причин прокрастинации

---

### **ЭТАП 7: Вводный инструктаж** 🎓
**Что делаем:** В самом конце, когда все тексты утверждены

**Содержание:**
- Философия раннего начала
- Принцип "план минимум"  
- Объяснение системы эскалации
- Важность регулярности

---

## 🔄 СВЯЗЬ С ДРУГИМИ ЗАДАЧАМИ

### **Следующая задача: Адаптивные интервалы**
- Автоматический расчет оптимальной длительности
- Анализ паттернов работы
- Персонализация под пользователя

### **Задача 4: Система барьеров**
- Глубокая проработка причин прокрастинации
- Многоуровневая помощь с планированием
- Интерактивная система преодоления барьеров

---

## 📝 ТЕХНИЧЕСКИЕ ЗАМЕТКИ

### **Количество напоминаний в день:**
Зависит от дней без работы:
- 0 дней → 2-3 напоминания
- 1-2 дня → 3-4 напоминания  
- 3+ дней → 4-5 напоминаний
- Неделя+ → до полноэкранной эскалации

### **Принцип формирования сообщений:**
1. Анализ дней без работы → выбор типа первого сообщения
2. Отслеживание ответов → дескалация интервалов
3. Проверка времени → повторные предложения
4. Финальная эскалация → полноэкранное сообщение

### **Интеграция с существующими системами:**
- ProjectManager (приоритетный проект)
- MotivationManager (мотивационные сообщения)
- UnifiedReminderSystem (система напоминаний)
- WorkPatternAnalyzer (анализ паттернов)

---

## ✅ КРИТЕРИИ ГОТОВНОСТИ

- [ ] Детектор пробуждения работает
- [ ] Аналитика дней без работы ведется
- [ ] Система сообщений с двойной эскалацией реализована
- [ ] План минимум интегрирован
- [ ] Адаптивные интервалы работают (хардкод)
- [ ] Заглушки для барьеров готовы
- [ ] Вводный инструктаж создан

**Готовность к следующей задаче:** Когда пользователь регулярно получает персонализированные предложения начать работу и система адаптируется под его поведение.
