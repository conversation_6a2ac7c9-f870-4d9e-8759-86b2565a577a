# 🌅 ЗАДАЧА: СИСТЕМА РАННЕГО ВОВЛЕЧЕНИЯ

## 🎯 ГЛАВНАЯ ЦЕЛЬ
Помочь пользователю **регулярно начинать работу** над приоритетным проектом утром с помощью **двойной системы эскалации**, принципа **"план минимум"** и **адаптивных интервалов**.

## 🔄 КЛЮЧЕВЫЕ ПРИНЦИПЫ

### **Принцип "Интервал начинается сразу"**
Как только пользователь отвечает на любой вопрос → интервал уже начался, продолжаем направлять его в работу.

### **Двойная система эскалации:**
1. **По дням без работы** (долгосрочная) - влияет на тип первого сообщения
2. **По времени в течение дня** (краткосрочная) - повторные предложения с дескалацией интервалов

### **Дескалация интервалов в течение дня:**
- Первое предложение: полный интервал (например, 52 минуты)
- Второе предложение: сокращенный интервал (например, 25 минут)  
- Третье предложение: план минимум (2-3 минуты)
- Финальное: полноэкранное "сейчас не начнешь - сегодня не начнешь"

### **Адаптивные интервалы по дням:**
- Работал вчера → стандартный интервал (52 мин)
- Не работал 1 день → сокращенный интервал (30 мин)
- Не работал 2+ дня → короткий интервал (15 мин)
- План минимум всегда доступен (2-3 мин)

---

## 📋 ЭТАПЫ РЕАЛИЗАЦИИ

### **ЭТАП 1: Детектор пробуждения** 🌅
**Что делаем:** Система понимает, когда пользователь проснулся и включил компьютер

**Результат:** 
- Предложение начать работу в самый продуктивный момент
- Стартовые сообщения сразу после пробуждения

**Технические детали:**
- Отслеживание времени последней активности
- Определение "первого запуска дня"
- Интеграция с системой уведомлений

---

### **ЭТАП 2: Умная аналитика дней без работы** 📊
**Что делаем:** Отслеживаем статистику работы над приоритетным проектом

**Результат:**
- Система "помнит" историю работы
- Основа для адаптивных сообщений и интервалов
- Статистика для эскалации

**Категории (обновленная градация):**
- **Уровень 0** (работал вчера) → позитивный настрой, стандартные интервалы
- **Уровень 1** (вчера не работал) → мягкое напоминание, понимающий тон
- **Уровень 2-3** (2-3 дня не работал) → усиленная мотивация, настойчивый тон
- **Уровень 4-6** (4-6 дней не работал) → серьезная эскалация, прямой тон
- **Уровень 7+** (неделя+ не работал) → критическая эскалация, полный экран

**🔮 Будущие улучшения:**
- Анализ постоянства за последнюю неделю/месяц
- Учет долгосрочных паттернов работы
- Персонализация сообщений под показатель постоянства
- Подстройка тона под общую статистику пользователя

---

### **ЭТАП 3: Система умных сообщений с двойной эскалацией** 💬🚨

## 📊 МАТРИЦА ПОСТРОЕНИЯ СООБЩЕНИЙ

### **Принципы логики построения сообщений:**

**Двумерная матрица (5 x 4 = 20 вариантов сообщений):**

**📅 ВЕРТИКАЛЬ - Дни без работы (влияет на ПРОДОЛЖИТЕЛЬНОСТЬ ПЕРВОГО ИНТЕРВАЛА ДНЯ):**
> Позже будут добавлен анализ истории дальше чем вчера. Работал ли последнюю неделю (сколько дней), а месяц? Или просто будем выводить данные показателя "постоянства" (пока не знаю как его формировать)
- **Уровень 0**: Работал вчера → позитивный тон, стандартный интервал (52 мин)
- **Уровень 1**: Вчера не работал → понимающий тон, стандартный интервал (52 мин)
- **Уровень 2-3**: 2-3 дня → мотивирующий тон, сокращенный интервал (30 мин)
- **Уровень 4-6**: 4-6 дней → серьезный тон, короткий интервал (15 мин)
- **Уровень 7+**: Неделя+ → критический тон, план минимум (3 мин)

**⏰ ГОРИЗОНТАЛЬ - Эскалация в течение дня (влияет на ПРОДОЛЖИТЕЛЬНОСТЬ ИНТЕРВАЛА):**
- **Предложение 1**: Утреннее → полный адаптивный интервал (сразу при обнаружения активности)
- **Предложение 2**: Повторное → дескалация интервала (-50%)
- **Предложение 3**: Настойчивое → план минимум (15 мин)
- **Предложение 4**: Финальное → полный экран "последний шанс" с план-минимум (2-3 мин) с помощью обнаружения барьера в будущем (возможно).

### **Логика выбора сообщения:**
1. **Определяем уровень** по дням без работы (0, 1, 2-3, 4-6, 7+)
2. **Определяем номер предложения** в течение дня (1, 2, 3, 4)
3. **Выбираем ячейку матрицы** с соответствующим тоном и интервалом
4. **Формируем сообщение** с учетом обоих факторов

---

#### **3.1 Примеры сообщений по матрице:**

**УРОВЕНЬ 0 (работал вчера) - Первое предложение:**
```
"Доброе утро! Готовы продолжить работу над [Проект]?
Вчера отлично поработали, продолжаем в том же духе!

Предлагаю стандартный интервал 52 минуты.

[Начать 52 мин] [Другой интервал] [Не сейчас]"
```

**УРОВЕНЬ 1 (вчера не работал) - Первое предложение:**
```
"Привет! Понимаю, вчера был сложный день.
Давайте сегодня вернемся к [Проект].

Предлагаю стандартный интервал 52 минуты.

[Начать 52 мин] [Сократить интервал] [Не сейчас]"
```

**УРОВЕНЬ 2-3 (2-3 дня не работал) - Первое предложение:**
```
"Время вернуться к [Проект]!
Несколько дней перерыва - это нормально, главное не затягивать.

Предлагаю сокращенный интервал 30 минут для мягкого возвращения.

[Начать 30 мин] [План минимум 3 мин] [Не сейчас]"
```

**УРОВЕНЬ 4-6 (4-6 дней не работал) - Первое предложение:**
```
"Серьезно скучаю по работе над [Проект]!
Уже почти неделя без прогресса - пора действовать.

Предлагаю короткий интервал 15 минут для возвращения в ритм.

[Начать 15 мин] [План минимум 3 мин] [Не сейчас]"
```

**УРОВЕНЬ 7+ (неделя+ не работал) - Первое предложение (ПОЛНЫЙ ЭКРАН):**
```
"🚨 КРИТИЧЕСКАЯ СИТУАЦИЯ!

Неделя без работы над [Проект]

Этот проект все еще важен для вас?
Если да - начинаем прямо сейчас с минимума.
Если нет - честно закрываем.

[Да, начать 3 мин] [Закрыть проект] [Еще день подумать]"
```

#### **3.2 Примеры эскалации в течение дня (для разных уровней):**

**УРОВЕНЬ 0-1 - Второе предложение (дескалация интервала):**
```
"Может, попробуем сокращенный интервал?
Лучше 25 минут сейчас, чем откладывать на потом.

[Начать 25 мин] [План минимум 3 мин] [Не сегодня]"
```

**УРОВЕНЬ 2-3 - Второе предложение (дескалация интервала):**
```
"Понимаю, 30 минут может показаться много.
Давайте 15 минут - это совсем немного!

[Начать 15 мин] [План минимум 3 мин] [Не сегодня]"
```

**ЛЮБОЙ УРОВЕНЬ - Третье предложение (план минимум):**
```
"Хорошо, давайте план минимум - всего 3 минуты.
Streak не прервется, регулярность важнее интенсивности.

[План минимум 3 мин] [Не сегодня]"
```

**ЛЮБОЙ УРОВЕНЬ - Финальное предложение (ПОЛНЫЙ ЭКРАН):**
```
"⏰ ПОСЛЕДНИЙ ШАНС СЕГОДНЯ

Если не начнешь СЕЙЧАС, то не начнешь СЕГОДНЯ.

Всего 2 минуты - и день не пропадет зря.

[2 минуты] [Честно: не хочу сегодня]"
```

#### **3.3 Проверка готовности (встроена в сообщения):**

**Вариант с проверкой ясности:**
```
"Готовы поработать над [Проект]?

Но сначала: ВАМ ЯСНО ЧТО ДЕЛАТЬ?

[Да, ясно - начать] [Не очень - проясню] [Совсем не ясно]"
```

**Ответы:**
- **"Да, ясно"** → Начинаем предложенный интервал
- **"Не очень"** → "Проясни задачи - это тоже считается интервалом!" → Начинаем интервал планирования
- **"Совсем не ясно"** → "10 минут на прояснение - и это уже работа!" → Интервал прояснения

---

### **ЭТАП 4: Система "План минимум"** ⭐
**Что делаем:** Внедряем принцип минимальной планки каждый день

**Ключевые принципы:**
- **"Лучше 2 минуты, чем ничего"**
- **"Одно отжимание лучше нуля"** 
- **Сохранение streak'а и регулярности**
- **Честный выбор: план минимум или "не хочу сегодня"**

**Варианты плана минимум:**
- 2-3 минуты работы
- Прочитать одну страницу
- Написать одно предложение  
- Открыть файл проекта и посмотреть

**Рост плана минимум:**
- Начинаем с 2 минут
- Постепенно растим до 5 минут (отдельная задача)

---

### **ЭТАП 5: Адаптивные интервалы** ⚙️
**Что делаем:** Автоматически подстраиваем длительность интервалов под историю работы

**Логика адаптации (обновленная):**
- **Уровень 0** (работал вчера) → стандартный интервал (52 мин)
- **Уровень 1** (вчера не работал) → стандартный интервал (52 мин)
- **Уровень 2-3** (2-3 дня) → сокращенный интервал (30 мин)
- **Уровень 4-6** (4-6 дней) → короткий интервал (15 мин)
- **Уровень 7+** (неделя+) → план минимум (3 мин)

**Дескалация в течение дня:**
- Первое предложение: адаптивный интервал (по таблице выше)
- Второе предложение: -50% от первого интервала
- Третье предложение: план минимум (3 мин)
- Финальное предложение: план минимум (2 мин)

**Комментарий:** Пока делаем хардкод значений, но подразумеваем автоматический расчет в будущем на основе анализа паттернов.

---

### **ЭТАП 6: Заглушки для системы барьеров** 🔧
**Что делаем:** Простые текстовые ответы на барьеры, пометки для будущего

**Заглушки:**
- **Неясность задач** → "Проясни - это тоже интервал!" → Интервал планирования
- **Лень** → "План минимум спасет streak" → Предложение 2-3 минут
- **Нет времени** → "2 минуты всегда найдутся" → План минимум
- **Проект не важен** → "Тогда закрой честно" → Опция закрытия проекта

**🔮 Пометки для будущего (Задача 4 - Барьеры):**
- Многоуровневая интерактивная система (4-10 уровней вопросов)
- Глубокая проработка каждого барьера
- Система помощи с планированием и прояснением
- Анализ причин прокрастинации

---

### **ЭТАП 7: Вводный инструктаж** 🎓
**Что делаем:** В самом конце, когда все тексты утверждены

**Содержание:**
- Философия раннего начала
- Принцип "план минимум"  
- Объяснение системы эскалации
- Важность регулярности

---

## 🔄 СВЯЗЬ С ДРУГИМИ ЗАДАЧАМИ

### **Следующая задача: Адаптивные интервалы**
- Автоматический расчет оптимальной длительности
- Анализ паттернов работы
- Персонализация под пользователя

### **Задача 4: Система барьеров**
- Глубокая проработка причин прокрастинации
- Многоуровневая помощь с планированием
- Интерактивная система преодоления барьеров

---

## 📝 ТЕХНИЧЕСКИЕ ЗАМЕТКИ

### **Количество напоминаний в день:**
Зависит от дней без работы:
- 0 дней → 2-3 напоминания
- 1-2 дня → 3-4 напоминания  
- 3+ дней → 4-5 напоминаний
- Неделя+ → до полноэкранной эскалации

### **Принцип формирования сообщений:**
1. Анализ дней без работы → выбор типа первого сообщения
2. Отслеживание ответов → дескалация интервалов
3. Проверка времени → повторные предложения
4. Финальная эскалация → полноэкранное сообщение

### **Интеграция с существующими системами:**
- ProjectManager (приоритетный проект)
- MotivationManager (мотивационные сообщения)
- UnifiedReminderSystem (система напоминаний)
- WorkPatternAnalyzer (анализ паттернов)

---

## ✅ КРИТЕРИИ ГОТОВНОСТИ

- [ ] Детектор пробуждения работает
- [ ] Аналитика дней без работы ведется
- [ ] Система сообщений с двойной эскалацией реализована
- [ ] План минимум интегрирован
- [ ] Адаптивные интервалы работают (хардкод)
- [ ] Заглушки для барьеров готовы
- [ ] Вводный инструктаж создан

**Готовность к следующей задаче:** Когда пользователь регулярно получает персонализированные предложения начать работу и система адаптируется под его поведение.
